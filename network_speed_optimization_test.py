#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网络连接速度优化测试
全面检查和优化网络连接慢的问题，使用真实cookies进行测试
"""

import sys
import time
import logging
import os
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.enhanced_browser_manager import EnhancedBrowserManager
from src.core.network_issue_resolver import NetworkIssueResolver
from src.models.account import Account
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('network_speed_test.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

class NetworkSpeedOptimizer:
    """网络速度优化器"""
    
    def __init__(self):
        self.cookies_dir = Path("data/cookies")
        self.test_results = []
        
    def get_available_accounts(self) -> List[str]:
        """获取可用的账号列表"""
        accounts = []
        if self.cookies_dir.exists():
            for cookie_file in self.cookies_dir.glob("*.cookies"):
                account_name = cookie_file.stem.replace("_sina_com", "@sina.com")
                accounts.append(account_name)
        return accounts
    
    def load_cookies_for_account(self, account_email: str) -> Optional[List[Dict]]:
        """加载指定账号的cookies（支持加密格式）"""
        try:
            # 转换邮箱格式为文件名格式
            file_name = account_email.replace("@sina.com", "_sina_com.cookies")
            cookie_file = self.cookies_dir / file_name

            if not cookie_file.exists():
                logger.warning(f"Cookie文件不存在: {cookie_file}")
                return None

            # 检查文件是否为空
            if cookie_file.stat().st_size == 0:
                logger.warning(f"Cookie文件为空: {cookie_file}")
                return None

            # 读取文件内容
            with open(cookie_file, 'r', encoding='utf-8') as f:
                file_content = f.read().strip()

            if not file_content:
                logger.warning(f"Cookie文件内容为空: {cookie_file}")
                return None

            # 尝试解密（如果是加密格式）
            try:
                from src.utils.encryption import get_encryption_manager
                encryption_manager = get_encryption_manager()

                # 尝试解密
                decrypted_content = encryption_manager.decrypt(file_content)
                if decrypted_content:
                    cookies_data = json.loads(decrypted_content)
                    logger.info(f"✅ 解密Cookie成功: {account_email}")
                else:
                    # 如果解密失败，尝试直接解析JSON
                    cookies_data = json.loads(file_content)
                    logger.info(f"✅ 直接解析Cookie成功: {account_email}")

            except Exception as decrypt_error:
                logger.debug(f"解密失败，尝试直接解析: {decrypt_error}")
                try:
                    # 尝试直接解析JSON
                    cookies_data = json.loads(file_content)
                    logger.info(f"✅ 直接解析Cookie成功: {account_email}")
                except Exception as json_error:
                    logger.error(f"❌ Cookie解析失败: {account_email} - {json_error}")
                    return None

            # 提取cookies列表
            if isinstance(cookies_data, dict) and 'cookies' in cookies_data:
                cookies = cookies_data['cookies']
            elif isinstance(cookies_data, list):
                cookies = cookies_data
            else:
                logger.error(f"Cookie格式不正确: {cookie_file}")
                return None

            logger.info(f"✅ 加载Cookie成功: {account_email} ({len(cookies)} 个)")
            return cookies

        except Exception as e:
            logger.error(f"❌ 加载Cookie失败: {account_email} - {e}")
            return None
    
    def create_ultra_fast_chrome_options(self) -> ChromeOptions:
        """创建超快速Chrome选项"""
        options = ChromeOptions()

        logger.info("🚀 创建超快速Chrome选项...")

        # 1. 核心速度优化 - 修复渲染器超时问题
        speed_args = [
            '--no-sandbox',                        # 禁用沙箱，提升速度
            '--disable-dev-shm-usage',             # 禁用/dev/shm使用
            '--disable-gpu',                       # 禁用GPU加速
            '--disable-software-rasterizer',       # 禁用软件光栅化
            '--disable-background-timer-throttling', # 禁用后台定时器节流
            '--disable-backgrounding-occluded-windows', # 禁用后台窗口
            '--disable-renderer-backgrounding',    # 禁用渲染器后台
            '--disable-features=TranslateUI',      # 禁用翻译UI
            '--disable-ipc-flooding-protection',   # 禁用IPC洪水保护
            '--disable-hang-monitor',              # 禁用挂起监控
            '--disable-prompt-on-repost',          # 禁用重新提交提示
            '--disable-domain-reliability',        # 禁用域名可靠性
        ]
        
        # 2. 网络连接优化 - 专门针对连接速度
        network_args = [
            '--no-proxy-server',                   # 禁用代理服务器
            '--proxy-bypass-list=*',               # 绕过所有代理
            '--disable-proxy-certificate-handler', # 禁用代理证书处理
            '--disable-background-networking',     # 禁用后台网络
            '--disable-default-apps',              # 禁用默认应用
            '--disable-sync',                      # 禁用同步
            '--disable-translate',                 # 禁用翻译
            '--disable-plugins',                   # 禁用插件
            '--disable-extensions',                # 禁用扩展
            '--disable-component-extensions-with-background-pages', # 禁用后台扩展
        ]
        
        # 3. SSL和安全优化 - 减少握手时间
        ssl_args = [
            '--ignore-ssl-errors',                 # 忽略SSL错误
            '--ignore-certificate-errors',         # 忽略证书错误
            '--ignore-certificate-errors-spki-list', # 忽略证书错误列表
            '--ignore-certificate-errors-ssl-errors', # 忽略SSL证书错误
            '--allow-running-insecure-content',    # 允许不安全内容
            '--disable-ssl-false-start',           # 禁用SSL假启动
            '--disable-web-security',              # 禁用Web安全
            '--disable-features=VizDisplayCompositor', # 禁用显示合成器
        ]
        
        # 4. 内存和缓存优化 - 减少内存使用
        memory_args = [
            '--memory-pressure-off',               # 关闭内存压力检测
            '--max_old_space_size=4096',          # 限制内存使用
            '--disable-background-downloads',      # 禁用后台下载
            '--disable-client-side-phishing-detection', # 禁用钓鱼检测
            '--disable-component-update',          # 禁用组件更新
            '--disable-domain-reliability',        # 禁用域名可靠性
        ]
        
        # 5. 页面加载优化 - 减少不必要的加载
        loading_args = [
            '--disable-images',                    # 禁用图片加载
            '--disable-javascript',               # 暂时禁用JS（测试连接时）
            '--disable-plugins',                   # 禁用插件
            '--disable-java',                      # 禁用Java
            '--disable-flash',                     # 禁用Flash
            '--blink-settings=imagesEnabled=false', # 禁用图片
        ]
        
        # 合并所有参数
        all_args = speed_args + network_args + ssl_args + memory_args + loading_args
        
        for arg in all_args:
            options.add_argument(arg)
        
        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # 设置窗口大小
        options.add_argument('--window-size=1280,720')
        
        # 禁用自动化标识
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        logger.info(f"   ✅ 配置了 {len(all_args)} 个超快速优化参数")
        return options
    
    def test_connection_speed_only(self) -> Tuple[bool, float, str]:
        """仅测试连接速度（不加载完整页面）"""
        logger.info("🌐 测试纯连接速度...")

        options = self.create_ultra_fast_chrome_options()

        # 进一步优化：只测试连接，不加载内容
        options.add_argument('--disable-javascript')
        options.add_argument('--disable-images')
        options.add_argument('--disable-css')
        options.add_argument('--headless')  # 无头模式更快

        driver = None
        try:
            start_time = time.time()

            # 创建驱动
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)

            # 设置更短的超时时间
            driver.set_page_load_timeout(3)
            driver.implicitly_wait(1)

            # 使用更快的方式访问页面
            try:
                driver.get("https://mail.sina.com.cn/")
            except Exception as timeout_error:
                # 即使超时也检查是否部分加载成功
                logger.debug(f"页面加载超时，但继续检查: {timeout_error}")

            connection_time = time.time() - start_time

            # 检查是否成功连接
            try:
                current_url = driver.current_url
                success = "sina.com" in current_url or "mail" in current_url
            except:
                success = False
                current_url = "无法获取URL"

            logger.info(f"   ⚡ 纯连接测试: {'成功' if success else '失败'} ({connection_time:.3f}秒)")

            return success, connection_time, current_url

        except Exception as e:
            connection_time = time.time() - start_time
            logger.error(f"   ❌ 连接测试失败: {e} ({connection_time:.3f}秒)")
            return False, connection_time, str(e)

        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass
    
    def test_with_real_cookies(self, account_email: str) -> Dict:
        """使用真实cookies进行完整测试"""
        logger.info(f"🍪 使用真实Cookie测试: {account_email}")
        
        result = {
            'account': account_email,
            'success': False,
            'times': {},
            'details': [],
            'errors': []
        }
        
        # 1. 加载cookies
        cookies = self.load_cookies_for_account(account_email)
        if not cookies:
            result['errors'].append("无法加载cookies")
            return result
        
        result['details'].append(f"✅ 加载了 {len(cookies)} 个Cookie")
        
        driver = None
        try:
            # 2. 创建优化的浏览器
            start_time = time.time()
            
            options = self.create_ultra_fast_chrome_options()
            # 重新启用JavaScript用于cookie测试 - 修复属性访问错误
            # 移除禁用JavaScript的参数
            new_args = []
            for arg in options.arguments:
                if '--disable-javascript' not in arg:
                    new_args.append(arg)

            # 重新创建选项以避免属性错误
            options = ChromeOptions()
            for arg in new_args:
                options.add_argument(arg)
            
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            
            browser_creation_time = time.time() - start_time
            result['times']['browser_creation'] = browser_creation_time
            result['details'].append(f"⚡ 浏览器创建: {browser_creation_time:.3f}秒")
            
            # 3. 快速导航到新浪邮箱
            start_time = time.time()
            driver.get("https://mail.sina.com.cn/")
            navigation_time = time.time() - start_time
            result['times']['navigation'] = navigation_time
            result['details'].append(f"🌐 页面导航: {navigation_time:.3f}秒")
            
            # 4. 应用cookies
            start_time = time.time()
            driver.delete_all_cookies()
            
            applied_count = 0
            for cookie in cookies:
                try:
                    clean_cookie = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie.get('domain', '.sina.com.cn'),
                        'path': cookie.get('path', '/'),
                    }
                    driver.add_cookie(clean_cookie)
                    applied_count += 1
                except Exception as e:
                    logger.debug(f"跳过Cookie {cookie.get('name', 'unknown')}: {e}")
            
            cookie_time = time.time() - start_time
            result['times']['cookie_application'] = cookie_time
            result['details'].append(f"🍪 Cookie应用: {cookie_time:.3f}秒 ({applied_count}/{len(cookies)})")
            
            # 5. 检查登录状态（不刷新页面）
            start_time = time.time()
            
            # 使用JavaScript快速检查登录状态
            login_check_script = """
                var indicators = ['写信', '收件箱', '发件箱', '草稿箱', 'compose', 'inbox'];
                var text = document.body.innerText || document.body.textContent || '';
                var found = [];
                for (var i = 0; i < indicators.length; i++) {
                    if (text.indexOf(indicators[i]) !== -1) {
                        found.push(indicators[i]);
                    }
                }
                return {
                    found: found,
                    url: window.location.href,
                    title: document.title,
                    textLength: text.length
                };
            """
            
            login_info = driver.execute_script(login_check_script)
            login_check_time = time.time() - start_time
            result['times']['login_check'] = login_check_time
            
            # 6. 评估结果
            is_logged_in = len(login_info['found']) > 0
            result['success'] = is_logged_in
            result['login_info'] = login_info
            
            if is_logged_in:
                result['details'].append(f"✅ 登录检查: {login_check_time:.3f}秒 - 找到指示器: {login_info['found']}")
            else:
                result['details'].append(f"⚠️ 登录检查: {login_check_time:.3f}秒 - 未找到登录指示器")
            
            # 7. 计算总时间
            total_time = sum(result['times'].values())
            result['times']['total'] = total_time
            result['details'].append(f"⏱️ 总耗时: {total_time:.3f}秒")
            
            logger.info(f"   🎯 测试完成: {'成功' if result['success'] else '失败'} (总耗时: {total_time:.3f}秒)")
            
            return result
            
        except Exception as e:
            logger.error(f"   ❌ 测试异常: {e}")
            result['errors'].append(f"测试异常: {str(e)}")
            return result
            
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass
    
    def run_comprehensive_speed_test(self):
        """运行综合速度测试"""
        print("🚀 网络连接速度全面优化测试")
        print("="*80)
        
        # 1. 纯连接速度测试
        print("\n📊 第一阶段：纯连接速度测试")
        print("-" * 40)
        
        for i in range(3):
            print(f"🔄 第 {i+1} 次连接测试...")
            success, conn_time, info = self.test_connection_speed_only()
            print(f"   结果: {'✅ 成功' if success else '❌ 失败'} - {conn_time:.3f}秒")
            if not success:
                print(f"   详情: {info}")
        
        # 2. 真实Cookie测试
        print("\n📊 第二阶段：真实Cookie登录测试")
        print("-" * 40)
        
        accounts = self.get_available_accounts()
        if not accounts:
            print("❌ 未找到可用的Cookie文件")
            return
        
        print(f"📋 找到 {len(accounts)} 个可用账号:")
        for account in accounts[:5]:  # 只显示前5个
            print(f"   - {account}")
        
        # 测试前3个账号
        test_accounts = accounts[:3]
        
        for i, account in enumerate(test_accounts, 1):
            print(f"\n🧪 测试 {i}/{len(test_accounts)}: {account}")
            print("-" * 30)
            
            result = self.test_with_real_cookies(account)
            
            # 显示详细结果
            for detail in result['details']:
                print(f"   {detail}")
            
            if result['errors']:
                for error in result['errors']:
                    print(f"   ❌ {error}")
            
            # 显示时间分解
            if result['times']:
                print(f"   📊 时间分解:")
                for phase, duration in result['times'].items():
                    print(f"      {phase}: {duration:.3f}秒")
            
            self.test_results.append(result)
        
        # 3. 性能分析和建议
        self.analyze_performance()
    
    def analyze_performance(self):
        """分析性能并提供优化建议"""
        print("\n📈 性能分析和优化建议")
        print("="*80)
        
        if not self.test_results:
            print("❌ 没有测试结果可分析")
            return
        
        # 统计成功率
        successful_tests = [r for r in self.test_results if r['success']]
        success_rate = len(successful_tests) / len(self.test_results) * 100
        
        print(f"📊 总体统计:")
        print(f"   测试账号数: {len(self.test_results)}")
        print(f"   成功率: {success_rate:.1f}% ({len(successful_tests)}/{len(self.test_results)})")
        
        if successful_tests:
            # 分析时间分布
            avg_times = {}
            for phase in ['browser_creation', 'navigation', 'cookie_application', 'login_check', 'total']:
                times = [r['times'].get(phase, 0) for r in successful_tests if phase in r['times']]
                if times:
                    avg_times[phase] = sum(times) / len(times)
            
            print(f"\n⏱️ 平均耗时分析:")
            for phase, avg_time in avg_times.items():
                print(f"   {phase}: {avg_time:.3f}秒")
            
            # 找出最慢的环节
            if avg_times:
                slowest_phase = max(avg_times.items(), key=lambda x: x[1])
                print(f"\n🐌 最慢环节: {slowest_phase[0]} ({slowest_phase[1]:.3f}秒)")
                
                # 提供针对性建议
                self.provide_optimization_suggestions(slowest_phase[0], avg_times)
        
        # 分析失败原因
        failed_tests = [r for r in self.test_results if not r['success']]
        if failed_tests:
            print(f"\n❌ 失败分析:")
            error_counts = {}
            for test in failed_tests:
                for error in test['errors']:
                    error_counts[error] = error_counts.get(error, 0) + 1
            
            for error, count in error_counts.items():
                print(f"   {error}: {count} 次")
    
    def provide_optimization_suggestions(self, slowest_phase: str, avg_times: Dict):
        """提供优化建议"""
        print(f"\n💡 针对 '{slowest_phase}' 的优化建议:")
        
        if slowest_phase == 'browser_creation':
            print("   🔧 浏览器创建优化:")
            print("      - 使用浏览器实例池，避免重复创建")
            print("      - 启用 --single-process 模式")
            print("      - 禁用更多不必要的功能")
            
        elif slowest_phase == 'navigation':
            print("   🌐 页面导航优化:")
            print("      - 使用更短的超时时间")
            print("      - 禁用图片和CSS加载")
            print("      - 考虑使用无头模式")
            
        elif slowest_phase == 'cookie_application':
            print("   🍪 Cookie应用优化:")
            print("      - 过滤无效的Cookie")
            print("      - 批量应用Cookie")
            print("      - 简化Cookie结构")
            
        elif slowest_phase == 'login_check':
            print("   🔍 登录检查优化:")
            print("      - 使用更快的检测方法")
            print("      - 减少检测指标数量")
            print("      - 并行检测多个指标")
        
        # 总体建议
        total_time = avg_times.get('total', 0)
        if total_time > 10:
            print(f"\n⚠️ 总耗时过长 ({total_time:.3f}秒)，建议:")
            print("   1. 启用浏览器实例复用")
            print("   2. 使用无头模式")
            print("   3. 预加载常用页面")
            print("   4. 优化网络环境")

if __name__ == "__main__":
    print("🚀 启动网络连接速度优化测试...")
    
    optimizer = NetworkSpeedOptimizer()
    optimizer.run_comprehensive_speed_test()
    
    print("\n✅ 测试完成！")
    print("📝 详细日志已保存到: network_speed_test.log")
