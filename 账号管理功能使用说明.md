# 账号管理功能使用说明

## 🎉 新功能概述

本次更新为账号管理模块添加了以下重要功能：

1. **发送次数实时更新** - 账号发送次数列现在能准确显示每个账号的发送统计
2. **冷却状态管理** - 支持账号冷却倒计时，避免账号被频繁限制
3. **批量操作** - 支持批量设置冷却、批量解冷等操作
4. **智能账号过滤** - 发送时自动跳过冷却中的账号

## 📊 账号状态说明

### 状态类型
- **✅ 正常** - 账号可正常使用
- **🧊 冷却中 (剩余时间)** - 账号正在冷却，显示具体倒计时
- **🚫 永久停用** - 账号被永久禁用
- **⏸️ 停用** - 账号临时停用
- **❌ 错误** - 账号有错误

### 状态颜色
- 绿色背景：正常可用
- 黄色背景：冷却中
- 红色背景：永久停用
- 灰色背景：其他状态

## 🛠️ 使用方法

### 1. 查看账号状态
1. 打开程序，进入"账号管理"标签页
2. 在账号列表中查看"账号状态"列
3. 状态列会显示当前账号的状态和剩余冷却时间

### 2. 单个账号操作
1. 双击要编辑的账号行，或选中后点击"编辑"按钮
2. 在弹出的编辑对话框中：
   - 修改账号状态（下拉选择）
   - 设置冷却时间（分钟）
   - 输入冷却原因
3. 点击"确定"保存修改

### 3. 批量操作
1. 勾选要操作的账号（可多选）
2. 点击相应的批量操作按钮：
   - **🧊 批量冷却** - 为选中账号设置冷却
   - **🔥 批量解冷** - 解除选中账号的冷却状态
3. 按提示输入冷却时间和原因
4. 确认操作

### 4. 自动冷却机制
- 发送器检测到账号频繁限制时，会自动设置60分钟冷却
- 冷却中的账号不会被选择用于发送
- 冷却时间到期后，账号自动恢复可用状态

## 📈 发送次数统计

### 实时更新
- 每次邮件发送成功后，对应账号的发送次数会自动增加
- 支持多浏览器并发发送时的准确计数
- 统计数据保存在数据库中，重启程序后仍然保留

### 查看方法
1. 在账号管理界面的"发送次数"列查看
2. 数据实时更新，无需手动刷新

## ⚠️ 注意事项

### 冷却时间设置
- 建议根据实际情况设置合理的冷却时间
- 频繁发送被限制时，建议设置较长的冷却时间（如60分钟以上）
- 可以根据账号的历史表现调整冷却策略

### 账号状态管理
- 永久停用的账号不会被用于发送，请谨慎设置
- 冷却中的账号会自动从发送队列中排除
- 建议定期检查账号状态，及时处理异常账号

### 批量操作
- 批量操作会影响多个账号，请确认选择正确
- 批量冷却时建议设置统一的冷却时间
- 操作前会有确认提示，请仔细阅读

## 🔧 故障排除

### 发送次数不更新
1. 检查数据库连接是否正常
2. 确认发送器是否正常工作
3. 查看日志文件中是否有错误信息

### 冷却状态异常
1. 检查系统时间是否正确
2. 确认数据库中的冷却时间设置
3. 可以手动清除冷却状态重新设置

### 界面显示问题
1. 点击"刷新"按钮更新账号列表
2. 重启程序重新加载数据
3. 检查数据库文件是否完整

## 📞 技术支持

如果遇到问题，请：
1. 查看程序日志文件（logs目录）
2. 检查数据库文件是否正常
3. 运行测试脚本验证功能：`python test_account_management_features.py`

## 🎯 最佳实践

1. **定期检查账号状态** - 建议每天检查一次账号状态，及时处理异常
2. **合理设置冷却时间** - 根据发送频率和账号表现调整冷却策略
3. **使用批量操作** - 对于相同状态的多个账号，使用批量操作提高效率
4. **监控发送统计** - 定期查看发送次数统计，了解账号使用情况
5. **备份重要数据** - 定期备份数据库文件，防止数据丢失

---

**更新时间**: 2025-08-06  
**版本**: v1.0  
**功能状态**: ✅ 已完成并测试通过
