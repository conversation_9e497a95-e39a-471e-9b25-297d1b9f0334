#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试账号管理功能
验证发送次数更新、冷却状态管理、界面显示等功能
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.models.database import DatabaseManager
from src.models.account import Account, AccountManager
from src.utils.logger import get_logger

logger = get_logger("TestAccountManagement")

def test_database_migration():
    """测试数据库迁移功能"""
    logger.info("🧪 测试数据库迁移功能...")

    try:
        db_path = "data/test_sina_email_automation.db"
        db_manager = DatabaseManager(db_path)
        logger.info("✅ 数据库初始化成功，冷却字段应该已添加")
        return True
    except Exception as e:
        logger.error(f"❌ 数据库迁移失败: {e}")
        return False

def test_account_cooling_features():
    """测试账号冷却功能"""
    logger.info("🧪 测试账号冷却功能...")
    
    try:
        db_path = "data/test_sina_email_automation.db"
        db_manager = DatabaseManager(db_path)
        account_manager = AccountManager(db_manager)
        
        # 创建测试账号
        test_account = Account(
            email="<EMAIL>",
            password="test123",
            status="active"
        )
        
        # 添加账号
        account_id = account_manager.add_account(test_account)
        logger.info(f"✅ 创建测试账号: ID {account_id}")
        
        # 测试设置冷却
        success = account_manager.set_account_cooling(account_id, 30, "测试冷却")
        if success:
            logger.info("✅ 设置账号冷却成功")
        else:
            logger.error("❌ 设置账号冷却失败")
            return False
        
        # 验证冷却状态
        account = account_manager.get_account_by_id(account_id)
        if account and account.is_cooling():
            logger.info(f"✅ 账号冷却状态验证成功，剩余时间: {account.get_cooling_remaining_text()}")
        else:
            logger.error("❌ 账号冷却状态验证失败")
            return False
        
        # 测试清除冷却
        success = account_manager.clear_account_cooling(account_id)
        if success:
            logger.info("✅ 清除账号冷却成功")
        else:
            logger.error("❌ 清除账号冷却失败")
            return False
        
        # 验证冷却已清除
        account = account_manager.get_account_by_id(account_id)
        if account and not account.is_cooling():
            logger.info("✅ 账号冷却清除验证成功")
        else:
            logger.error("❌ 账号冷却清除验证失败")
            return False
        
        # 清理测试数据
        account_manager.delete_account(account_id)
        logger.info("✅ 清理测试数据完成")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试账号冷却功能失败: {e}")
        return False

def test_send_count_increment():
    """测试发送次数增加功能"""
    logger.info("🧪 测试发送次数增加功能...")
    
    try:
        db_path = "data/test_sina_email_automation.db"
        db_manager = DatabaseManager(db_path)
        account_manager = AccountManager(db_manager)
        
        # 创建测试账号
        test_account = Account(
            email="<EMAIL>",
            password="test123",
            status="active",
            send_count=0
        )
        
        # 添加账号
        account_id = account_manager.add_account(test_account)
        logger.info(f"✅ 创建测试账号: ID {account_id}")
        
        # 测试增加发送次数
        for i in range(5):
            success = account_manager.increment_send_count(account_id)
            if success:
                logger.info(f"✅ 第 {i+1} 次发送次数增加成功")
            else:
                logger.error(f"❌ 第 {i+1} 次发送次数增加失败")
                return False
        
        # 验证发送次数
        account = account_manager.get_account_by_id(account_id)
        if account and account.send_count == 5:
            logger.info(f"✅ 发送次数验证成功: {account.send_count}")
        else:
            logger.error(f"❌ 发送次数验证失败: 期望5，实际{account.send_count if account else 'None'}")
            return False
        
        # 清理测试数据
        account_manager.delete_account(account_id)
        logger.info("✅ 清理测试数据完成")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试发送次数增加功能失败: {e}")
        return False

def test_available_accounts_filtering():
    """测试可用账号过滤功能"""
    logger.info("🧪 测试可用账号过滤功能...")
    
    try:
        db_path = "data/test_sina_email_automation.db"
        db_manager = DatabaseManager(db_path)
        account_manager = AccountManager(db_manager)

        # 创建多个测试账号
        account_ids = []
        
        # 正常账号
        normal_account = Account(
            email="<EMAIL>",
            password="test123",
            status="active"
        )
        account_ids.append(account_manager.add_account(normal_account))
        
        # 冷却账号
        cooling_account = Account(
            email="<EMAIL>",
            password="test123",
            status="active"
        )
        cooling_id = account_manager.add_account(cooling_account)
        account_ids.append(cooling_id)
        account_manager.set_account_cooling(cooling_id, 30, "测试冷却")
        
        # 永久停用账号
        disabled_account = Account(
            email="<EMAIL>",
            password="test123",
            status="active"
        )
        disabled_id = account_manager.add_account(disabled_account)
        account_ids.append(disabled_id)
        account_manager.set_account_permanent_disabled(disabled_id)
        
        # 获取可用账号
        available_accounts = account_manager.get_available_accounts()
        available_emails = [acc.email for acc in available_accounts]
        
        # 验证过滤结果
        if "<EMAIL>" in available_emails:
            logger.info("✅ 正常账号在可用列表中")
        else:
            logger.error("❌ 正常账号不在可用列表中")
            return False
        
        if "<EMAIL>" not in available_emails:
            logger.info("✅ 冷却账号已被过滤")
        else:
            logger.error("❌ 冷却账号未被过滤")
            return False
        
        if "<EMAIL>" not in available_emails:
            logger.info("✅ 永久停用账号已被过滤")
        else:
            logger.error("❌ 永久停用账号未被过滤")
            return False
        
        # 清理测试数据
        for account_id in account_ids:
            account_manager.delete_account(account_id)
        logger.info("✅ 清理测试数据完成")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试可用账号过滤功能失败: {e}")
        return False

def test_batch_operations():
    """测试批量操作功能"""
    logger.info("🧪 测试批量操作功能...")
    
    try:
        db_path = "data/test_sina_email_automation.db"
        db_manager = DatabaseManager(db_path)
        account_manager = AccountManager(db_manager)
        
        # 创建多个测试账号
        account_ids = []
        for i in range(3):
            test_account = Account(
                email=f"test_batch_{i}@sina.com",
                password="test123",
                status="active"
            )
            account_ids.append(account_manager.add_account(test_account))
        
        logger.info(f"✅ 创建 {len(account_ids)} 个测试账号")
        
        # 测试批量设置冷却
        success_count = account_manager.batch_set_cooling(account_ids, 15, "批量测试冷却")
        if success_count == len(account_ids):
            logger.info("✅ 批量设置冷却成功")
        else:
            logger.error(f"❌ 批量设置冷却失败: {success_count}/{len(account_ids)}")
            return False
        
        # 验证冷却状态
        cooling_count = 0
        for account_id in account_ids:
            account = account_manager.get_account_by_id(account_id)
            if account and account.is_cooling():
                cooling_count += 1
        
        if cooling_count == len(account_ids):
            logger.info("✅ 批量冷却状态验证成功")
        else:
            logger.error(f"❌ 批量冷却状态验证失败: {cooling_count}/{len(account_ids)}")
            return False
        
        # 测试批量清除冷却
        success_count = account_manager.batch_clear_cooling(account_ids)
        if success_count == len(account_ids):
            logger.info("✅ 批量清除冷却成功")
        else:
            logger.error(f"❌ 批量清除冷却失败: {success_count}/{len(account_ids)}")
            return False
        
        # 验证冷却已清除
        non_cooling_count = 0
        for account_id in account_ids:
            account = account_manager.get_account_by_id(account_id)
            if account and not account.is_cooling():
                non_cooling_count += 1
        
        if non_cooling_count == len(account_ids):
            logger.info("✅ 批量清除冷却状态验证成功")
        else:
            logger.error(f"❌ 批量清除冷却状态验证失败: {non_cooling_count}/{len(account_ids)}")
            return False
        
        # 清理测试数据
        for account_id in account_ids:
            account_manager.delete_account(account_id)
        logger.info("✅ 清理测试数据完成")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试批量操作功能失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始测试账号管理功能...")
    
    tests = [
        ("数据库迁移", test_database_migration),
        ("账号冷却功能", test_account_cooling_features),
        ("发送次数增加", test_send_count_increment),
        ("可用账号过滤", test_available_accounts_filtering),
        ("批量操作", test_batch_operations),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
                failed += 1
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
            failed += 1
    
    logger.info(f"\n{'='*50}")
    logger.info(f"测试结果汇总")
    logger.info(f"{'='*50}")
    logger.info(f"✅ 通过: {passed}")
    logger.info(f"❌ 失败: {failed}")
    logger.info(f"📊 总计: {passed + failed}")
    
    if failed == 0:
        logger.info("🎉 所有测试都通过了！")
        return True
    else:
        logger.error(f"💥 有 {failed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
