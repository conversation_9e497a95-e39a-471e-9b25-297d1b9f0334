#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多浏览器集成适配器
将新开发的并发发送系统集成到现有的多浏览器发送界面中
"""

import time
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
from loguru import logger

from src.models.account import Account
from .smart_account_strategy import SmartAccountStrategy, SendingMode
from .concurrent_email_sender_manager import ConcurrentEmailSenderManager, ConcurrentSendingConfig
from .intelligent_task_distributor import IntelligentTaskDistributor
from .enhanced_cookie_account_switcher import EnhancedCookieAccountSwitcher


@dataclass
class IntegrationConfig:
    """集成配置"""
    enable_concurrent_mode: bool = True      # 启用并发模式
    auto_detect_browser_count: bool = True   # 自动检测浏览器数量
    max_browsers: int = 10                   # 最大浏览器数量
    send_interval: float = 2.0               # 发送间隔
    account_switch_threshold: int = 10       # 账号切换阈值
    enable_headless: bool = False            # 启用无头模式
    user_data_dir_base: str = "chrome_profiles"  # 用户数据目录基础路径


class MultiBrowserIntegrationAdapter:
    """多浏览器集成适配器"""
    
    def __init__(self, config: IntegrationConfig):
        self.config = config
        self.logger = logger
        
        # 核心组件
        self.strategy_analyzer = SmartAccountStrategy()
        self.concurrent_manager: Optional[ConcurrentEmailSenderManager] = None
        
        # 状态管理
        self.is_initialized = False
        self.current_strategy = None
        self.sending_stats = {}
        
        # 回调函数
        self.progress_callback: Optional[Callable] = None
        self.completion_callback: Optional[Callable] = None
        self.log_callback: Optional[Callable] = None
    
    def analyze_sending_requirements(
        self,
        accounts: List[Account],
        recipient_emails: List[str],
        user_browser_count: Optional[int] = None,
        sending_mode: str = "individual"
    ) -> Dict[str, Any]:
        """
        分析发送需求
        
        Args:
            accounts: 可用账号列表
            recipient_emails: 收件人邮箱列表
            user_browser_count: 用户指定的浏览器数量
            sending_mode: 发送模式（"individual" 或 "batch"）
            
        Returns:
            Dict: 分析结果
        """
        try:
            self.logger.info(f"📊 分析发送需求...")
            
            # 转换发送模式
            mode = SendingMode.INDIVIDUAL if sending_mode == "individual" else SendingMode.BATCH
            
            # 分析策略
            strategy = self.strategy_analyzer.analyze_sending_strategy(
                available_accounts=accounts,
                recipient_emails=recipient_emails,
                sending_mode=mode,
                user_browser_count=user_browser_count,
                emails_per_account=self.config.account_switch_threshold
            )
            
            self.current_strategy = strategy
            
            # 生成分析报告
            analysis_result = {
                'strategy': {
                    'browser_mode': strategy.browser_mode.value,
                    'browser_count': strategy.browser_count,
                    'emails_per_account': strategy.emails_per_account,
                    'total_emails': strategy.total_emails,
                    'estimated_time': strategy.estimated_time
                },
                'allocation': strategy.get_browser_allocation(),
                'recommendations': self._generate_recommendations(strategy, accounts, recipient_emails),
                'warnings': self._generate_warnings(strategy, accounts, recipient_emails)
            }
            
            self.logger.info(f"✅ 发送需求分析完成")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"分析发送需求失败: {e}")
            return {'error': str(e)}
    
    def initialize_concurrent_sender(self) -> bool:
        """初始化并发发送器"""
        try:
            if self.is_initialized:
                self.logger.info("并发发送器已初始化")
                return True
            
            self.logger.info("🔧 初始化并发发送器...")
            
            # 创建并发发送配置
            concurrent_config = ConcurrentSendingConfig(
                max_browsers=self.config.max_browsers,
                send_interval=self.config.send_interval,
                account_switch_threshold=self.config.account_switch_threshold,
                enable_headless=self.config.enable_headless,
                user_data_dir_base=self.config.user_data_dir_base
            )
            
            # 创建并发管理器
            self.concurrent_manager = ConcurrentEmailSenderManager(concurrent_config)
            
            # 设置回调函数
            if self.progress_callback:
                self.concurrent_manager.set_progress_callback(self._handle_progress_update)
            if self.completion_callback:
                self.concurrent_manager.set_completion_callback(self._handle_completion)
            
            self.is_initialized = True
            self.logger.info("✅ 并发发送器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化并发发送器失败: {e}")
            return False
    
    def start_sending(
        self,
        accounts: List[Account],
        recipient_emails: List[str],
        subject: str,
        content: str,
        content_type: str = "text/plain",
        cc_email: Optional[str] = None,
        sending_mode: str = "individual",
        batch_size: int = 1,
        user_browser_count: Optional[int] = None
    ) -> bool:
        """
        启动发送
        
        Args:
            accounts: 可用账号列表
            recipient_emails: 收件人邮箱列表
            subject: 邮件主题
            content: 邮件内容
            content_type: 内容类型
            cc_email: 抄送邮箱
            sending_mode: 发送模式
            batch_size: 批量大小
            user_browser_count: 用户指定的浏览器数量
            
        Returns:
            bool: 启动是否成功
        """
        try:
            if not self.is_initialized:
                if not self.initialize_concurrent_sender():
                    return False
            
            if not self.concurrent_manager:
                self.logger.error("并发管理器未初始化")
                return False
            
            self.logger.info("🚀 启动并发发送...")
            
            # 转换发送模式
            mode = SendingMode.INDIVIDUAL if sending_mode == "individual" else SendingMode.BATCH
            
            # 启动并发发送
            success = self.concurrent_manager.start_concurrent_sending(
                accounts=accounts,
                recipient_emails=recipient_emails,
                subject=subject,
                content=content,
                content_type=content_type,
                cc_email=cc_email,
                sending_mode=mode,
                batch_size=batch_size,
                user_browser_count=user_browser_count
            )
            
            if success:
                self.logger.info("✅ 并发发送启动成功")
                self._log_message("🚀 并发发送已启动，多个浏览器开始工作", "info")
            else:
                self.logger.error("❌ 并发发送启动失败")
                self._log_message("❌ 并发发送启动失败", "error")
            
            return success
            
        except Exception as e:
            self.logger.error(f"启动发送失败: {e}")
            self._log_message(f"启动发送失败: {e}", "error")
            return False
    
    def stop_sending(self):
        """停止发送"""
        try:
            if self.concurrent_manager:
                self.concurrent_manager.stop_sending()
                self._log_message("🛑 并发发送已停止", "info")
            
        except Exception as e:
            self.logger.error(f"停止发送失败: {e}")
            self._log_message(f"停止发送失败: {e}", "error")
    
    def pause_sending(self):
        """暂停发送"""
        try:
            if self.concurrent_manager:
                self.concurrent_manager.pause_sending()
                self._log_message("⏸️ 并发发送已暂停", "info")
            
        except Exception as e:
            self.logger.error(f"暂停发送失败: {e}")
            self._log_message(f"暂停发送失败: {e}", "error")
    
    def resume_sending(self):
        """恢复发送"""
        try:
            if self.concurrent_manager:
                self.concurrent_manager.resume_sending()
                self._log_message("▶️ 并发发送已恢复", "info")
            
        except Exception as e:
            self.logger.error(f"恢复发送失败: {e}")
            self._log_message(f"恢复发送失败: {e}", "error")
    
    def get_sending_stats(self) -> Dict[str, Any]:
        """获取发送统计"""
        try:
            if self.concurrent_manager:
                return self.concurrent_manager.get_sending_stats()
            return {}
            
        except Exception as e:
            self.logger.error(f"获取发送统计失败: {e}")
            return {}
    
    def _generate_recommendations(
        self,
        strategy,
        accounts: List[Account],
        recipient_emails: List[str]
    ) -> List[str]:
        """生成建议"""
        recommendations = []
        
        if strategy.browser_count == 1:
            recommendations.append("🔍 建议：当前使用单浏览器模式，如需提高发送速度可增加账号数量")
        else:
            recommendations.append(f"⚡ 建议：使用{strategy.browser_count}个浏览器并发发送，预计提高{strategy.browser_count}倍速度")
        
        if strategy.emails_per_account > 20:
            recommendations.append("⚠️ 建议：每账号发送数量较多，建议适当降低以避免被限制")
        
        if len(accounts) < strategy.browser_count:
            recommendations.append("📧 建议：账号数量少于浏览器数量，建议增加更多账号以充分利用并发能力")
        
        return recommendations
    
    def _generate_warnings(
        self,
        strategy,
        accounts: List[Account],
        recipient_emails: List[str]
    ) -> List[str]:
        """生成警告"""
        warnings = []
        
        if len(recipient_emails) > 1000:
            warnings.append("⚠️ 警告：收件人数量较多，请确保账号状态良好以避免被限制")
        
        if strategy.estimated_time > 60:
            warnings.append(f"⏰ 警告：预计发送时间较长（{strategy.estimated_time:.1f}分钟），请耐心等待")
        
        if len(accounts) == 0:
            warnings.append("❌ 错误：没有可用账号，无法发送邮件")
        
        return warnings
    
    def _handle_progress_update(self, stats: Dict[str, Any]):
        """处理进度更新"""
        try:
            self.sending_stats = stats
            
            if self.progress_callback:
                self.progress_callback(stats)
                
        except Exception as e:
            self.logger.error(f"处理进度更新失败: {e}")
    
    def _handle_completion(self, stats: Dict[str, Any]):
        """处理完成事件"""
        try:
            self._log_message("🎉 所有邮件发送完成", "success")
            
            if self.completion_callback:
                self.completion_callback(stats)
                
        except Exception as e:
            self.logger.error(f"处理完成事件失败: {e}")
    
    def _log_message(self, message: str, level: str = "info"):
        """记录日志消息"""
        try:
            if self.log_callback:
                self.log_callback(message, level)
            
            # 同时记录到日志
            if level == "error":
                self.logger.error(message)
            elif level == "warning":
                self.logger.warning(message)
            elif level == "success":
                self.logger.success(message)
            else:
                self.logger.info(message)
                
        except Exception as e:
            self.logger.error(f"记录日志消息失败: {e}")
    
    def set_progress_callback(self, callback: Callable):
        """设置进度回调"""
        self.progress_callback = callback
    
    def set_completion_callback(self, callback: Callable):
        """设置完成回调"""
        self.completion_callback = callback
    
    def set_log_callback(self, callback: Callable):
        """设置日志回调"""
        self.log_callback = callback
