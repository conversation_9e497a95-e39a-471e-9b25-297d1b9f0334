#!/usr/bin/env python3
"""
测试清理后的主界面
验证删除了不需要的标签页后，主界面能正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from src.utils.logger import setup_logger
from src.gui.main_window import MainWindow

logger = setup_logger("INFO")

def test_cleaned_main_window():
    """测试清理后的主界面"""
    try:
        logger.info("🧪 开始测试清理后的主界面...")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 显示窗口
        main_window.show()
        
        # 获取标签页数量
        tab_count = main_window.tab_widget.count()
        logger.info(f"📋 标签页总数: {tab_count}")
        
        # 列出所有标签页
        tab_names = []
        for i in range(tab_count):
            tab_text = main_window.tab_widget.tabText(i)
            tab_names.append(tab_text)
            logger.info(f"  标签页 {i+1}: {tab_text}")
        
        # 检查应该保留的标签页
        expected_tabs = [
            "👤 账号管理",
            "🌐 多浏览器发送",
            "🌐 代理管理", 
            "📁 文件监控",
            "📝 日志查看"
        ]
        
        # 检查不应该存在的标签页
        forbidden_tabs = [
            "📋 智能任务管理",
            "🚀 强大多浏览器发送", 
            "⚡ 并发发送器"
        ]
        
        # 验证保留的标签页
        missing_tabs = []
        for expected in expected_tabs:
            found = any(expected in tab_name for tab_name in tab_names)
            if found:
                logger.info(f"✅ 找到预期标签页: {expected}")
            else:
                missing_tabs.append(expected)
                logger.warning(f"⚠️ 缺少预期标签页: {expected}")
        
        # 验证删除的标签页
        found_forbidden = []
        for forbidden in forbidden_tabs:
            found = any(forbidden in tab_name for tab_name in tab_names)
            if found:
                found_forbidden.append(forbidden)
                logger.error(f"❌ 发现应该删除的标签页: {forbidden}")
            else:
                logger.info(f"✅ 成功删除标签页: {forbidden}")
        
        # 测试切换到每个标签页
        for i in range(tab_count):
            try:
                main_window.tab_widget.setCurrentIndex(i)
                tab_text = main_window.tab_widget.tabText(i)
                logger.info(f"✅ 成功切换到标签页: {tab_text}")
            except Exception as e:
                logger.error(f"❌ 切换到标签页 {i} 失败: {e}")
        
        # 总结测试结果
        success = len(missing_tabs) == 0 and len(found_forbidden) == 0
        
        if success:
            logger.info("🎉 主界面清理测试成功！")
            logger.info("✅ 所有不需要的标签页已成功删除")
            logger.info("✅ 所有需要保留的标签页都正常工作")
        else:
            logger.error("❌ 主界面清理测试失败")
            if missing_tabs:
                logger.error(f"缺少标签页: {missing_tabs}")
            if found_forbidden:
                logger.error(f"未删除的标签页: {found_forbidden}")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 测试主界面异常: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始主界面清理测试...")
    
    success = test_cleaned_main_window()
    
    if success:
        logger.info("🎉 主界面清理测试成功！")
        logger.info("现在您的主界面包含以下标签页：")
        logger.info("  1. 👤 账号管理 - 管理邮箱账号")
        logger.info("  2. 🌐 多浏览器发送 - 原有的多浏览器发送系统")
        logger.info("  3. 🌐 代理管理 - 代理服务器管理")
        logger.info("  4. 📁 文件监控 - 文件监控功能")
        logger.info("  5. 📝 日志查看 - 查看系统日志")
        logger.info("")
        logger.info("已成功删除的标签页：")
        logger.info("  ❌ 📋 智能任务管理")
        logger.info("  ❌ 🚀 强大多浏览器发送")
        logger.info("  ❌ ⚡ 并发发送器")
        logger.info("")
        logger.info("现在只保留原有的多浏览器发送功能！")
        return 0
    else:
        logger.error("❌ 主界面清理测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
