#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网络问题解决器
基于项目成功经验，专门解决浏览器打开mail.sina.com.cn的网络问题
"""

import time
import logging
from typing import Dict, List, Optional, Tuple
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

logger = logging.getLogger(__name__)

class NetworkIssueResolver:
    """网络问题解决器 - 基于成功经验"""
    
    def __init__(self):
        self.success_patterns = {
            'direct_connection': True,  # 优先使用直连
            'ssl_bypass': True,         # 绕过SSL验证
            'proxy_bypass': True,       # 绕过代理
            'cookie_reuse': True        # 启用Cookie复用
        }
        
        # 网络问题检测模式
        self.network_issues = [
            "Could not reach host",
            "Are you offline?",
            "net::ERR_PROXY_CONNECTION_FAILED",
            "net::ERR_INTERNET_DISCONNECTED",
            "net::ERR_NETWORK_CHANGED",
            "SSL handshake failed",
            "Certificate error"
        ]
    
    def create_optimized_chrome_options(self, account=None) -> ChromeOptions:
        """
        创建优化的Chrome选项 - 基于成功经验
        解决网络连接问题的核心配置
        """
        options = ChromeOptions()
        
        # 🔧 核心网络问题修复 - 基于成功经验
        logger.info("🔧 应用网络问题修复配置...")
        
        # 1. 代理问题修复 - 强制直连模式
        options.add_argument('--no-proxy-server')           # 禁用代理服务器
        options.add_argument('--proxy-bypass-list=*')       # 绕过所有代理
        options.add_argument('--disable-proxy-certificate-handler')
        options.add_argument('--disable-background-networking')
        logger.info("   ✅ 代理问题修复配置完成")
        
        # 2. SSL和证书问题修复
        options.add_argument('--ignore-ssl-errors')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-certificate-errors-spki-list')
        options.add_argument('--ignore-certificate-errors-ssl-errors')
        options.add_argument('--disable-ssl-false-start')
        options.add_argument('--allow-running-insecure-content')
        logger.info("   ✅ SSL证书问题修复配置完成")
        
        # 3. 网络安全设置优化
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer')
        options.add_argument('--enable-features=NetworkService,NetworkServiceLogging')
        options.add_argument('--disable-site-isolation-trials')
        options.add_argument('--disable-features=site-per-process')
        logger.info("   ✅ 网络安全设置优化完成")
        
        # 4. 基础稳定性配置
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        
        # 5. 反检测配置
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 6. 性能优化
        options.add_argument('--disable-images')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-renderer-backgrounding')
        
        # 7. 用户代理设置
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # 8. 窗口设置
        options.add_argument('--window-size=1280,720')
        
        logger.info("🚀 网络优化Chrome选项配置完成")
        return options
    
    def test_network_connectivity(self, driver) -> Tuple[bool, str]:
        """测试网络连接性"""
        try:
            logger.info("🌐 测试网络连接性...")
            
            # 尝试访问新浪邮箱
            start_time = time.time()
            driver.get("https://mail.sina.com.cn/")
            
            # 等待页面加载
            WebDriverWait(driver, 10).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            
            current_url = driver.current_url
            page_title = driver.title
            elapsed = time.time() - start_time
            
            # 检查是否成功访问
            if "mail.sina.com.cn" in current_url:
                logger.info(f"✅ 网络连接测试成功 ({elapsed:.2f}秒)")
                logger.info(f"   当前URL: {current_url}")
                logger.info(f"   页面标题: {page_title}")
                return True, f"网络连接正常，耗时{elapsed:.2f}秒"
            else:
                logger.error(f"❌ 网络连接测试失败 - URL异常: {current_url}")
                return False, f"URL异常: {current_url}"
                
        except TimeoutException:
            logger.error("❌ 网络连接测试超时")
            return False, "网络连接超时"
        except WebDriverException as e:
            error_msg = str(e)
            logger.error(f"❌ 网络连接测试失败: {error_msg}")
            
            # 检查是否是已知的网络问题
            for issue in self.network_issues:
                if issue in error_msg:
                    return False, f"网络问题: {issue}"
            
            return False, f"WebDriver异常: {error_msg}"
        except Exception as e:
            logger.error(f"❌ 网络连接测试异常: {e}")
            return False, f"未知异常: {str(e)}"
    
    def apply_cookie_login(self, driver, account_email: str) -> Tuple[bool, str]:
        """
        应用Cookie登录 - 基于成功经验
        避免页面刷新导致的会话断开
        """
        try:
            logger.info(f"🍪 开始Cookie登录: {account_email}")
            
            # 1. 确保在新浪邮箱域名下
            current_url = driver.current_url
            if "mail.sina.com.cn" not in current_url:
                logger.info("🌐 导航到新浪邮箱...")
                driver.get("https://mail.sina.com.cn/")
                time.sleep(2)
            
            # 2. 从现有Cookie管理器加载Cookie
            from src.core.cookie_manager import CookieManager
            
            config = {
                'cookie_dir': 'data/cookies',
                'encryption_enabled': True,
                'max_age_days': 30
            }
            cookie_manager = CookieManager(config)
            
            # 3. 获取Cookie数据
            cookie_data = cookie_manager.get_cookies(account_email)
            if not cookie_data:
                logger.warning(f"⚠️ 未找到Cookie数据: {account_email}")
                return False, "未找到Cookie数据"
            
            cookies = cookie_data.get('cookies', [])
            if not cookies:
                logger.warning(f"⚠️ Cookie数据为空: {account_email}")
                return False, "Cookie数据为空"
            
            logger.info(f"📦 加载到 {len(cookies)} 个Cookie")
            
            # 4. 清除现有Cookie并应用新Cookie
            driver.delete_all_cookies()
            
            applied_count = 0
            for cookie in cookies:
                try:
                    # 只保留必要字段，避免Cookie格式问题
                    clean_cookie = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie.get('domain', '.sina.com.cn'),
                        'path': cookie.get('path', '/'),
                    }
                    driver.add_cookie(clean_cookie)
                    applied_count += 1
                except Exception as e:
                    logger.debug(f"跳过Cookie {cookie.get('name', 'unknown')}: {e}")
            
            logger.info(f"✅ 应用Cookie: {applied_count}/{len(cookies)} 个")
            
            # 5. 关键修正：不刷新页面，直接检查登录状态
            # 避免页面刷新导致会话断开
            logger.info("🔍 检查登录状态（不刷新页面）...")
            
            # 使用JavaScript检查登录状态
            login_indicators = [
                "写信", "收件箱", "发件箱", "草稿箱",
                "compose", "inbox", "sent", "draft"
            ]
            
            page_text = driver.execute_script("return document.body.innerText;")
            is_logged_in = any(indicator in page_text for indicator in login_indicators)
            
            if is_logged_in:
                logger.info("✅ Cookie登录成功确认")
                return True, "Cookie登录成功"
            else:
                # 即使登录状态不确定，也不刷新页面
                logger.info("⚠️ 登录状态不确定，但保持浏览器会话")
                return True, "Cookie已应用，浏览器会话保持"
                
        except Exception as e:
            logger.error(f"❌ Cookie登录失败: {e}")
            return False, f"Cookie登录异常: {str(e)}"
    
    def check_login_status(self, driver) -> Tuple[bool, str]:
        """检查登录状态"""
        try:
            current_url = driver.current_url
            page_title = driver.title
            
            # 检查URL和标题
            if "mail.sina.com.cn" in current_url and "login" not in current_url.lower():
                # 进一步检查页面内容
                try:
                    # 查找登录后的特征元素
                    login_indicators = [
                        "写信", "收件箱", "发件箱", "草稿箱",
                        "compose", "inbox", "sent", "draft"
                    ]
                    
                    page_text = driver.execute_script("return document.body.innerText;")
                    
                    for indicator in login_indicators:
                        if indicator in page_text:
                            logger.info(f"✅ 登录状态确认: 找到指示器 '{indicator}'")
                            return True, f"已登录 - 找到指示器: {indicator}"
                    
                    # 如果没有找到明确的登录指示器，但URL正确
                    logger.info("⚠️ 登录状态不确定，但在邮箱域名下")
                    return True, "在邮箱域名下，可能已登录"
                    
                except Exception as e:
                    logger.warning(f"⚠️ 登录状态检查异常: {e}")
                    return True, "登录状态检查异常，但浏览器可用"
            else:
                logger.info(f"ℹ️ 当前在登录页面或其他页面: {current_url}")
                return False, f"需要登录: {current_url}"
                
        except Exception as e:
            logger.error(f"❌ 登录状态检查失败: {e}")
            return False, f"登录状态检查失败: {str(e)}"
    
    def resolve_network_issues(self, driver, account_email: str = None) -> Dict:
        """
        解决网络问题的完整流程
        基于项目成功经验
        """
        result = {
            'success': False,
            'network_test': False,
            'cookie_login': False,
            'login_status': False,
            'message': '',
            'details': []
        }
        
        try:
            logger.info("🔧 开始网络问题解决流程...")
            
            # 1. 网络连接测试
            network_success, network_msg = self.test_network_connectivity(driver)
            result['network_test'] = network_success
            result['details'].append(f"网络测试: {network_msg}")
            
            if not network_success:
                result['message'] = f"网络连接失败: {network_msg}"
                return result
            
            # 2. Cookie登录（如果提供了账号）
            if account_email:
                cookie_success, cookie_msg = self.apply_cookie_login(driver, account_email)
                result['cookie_login'] = cookie_success
                result['details'].append(f"Cookie登录: {cookie_msg}")
            else:
                result['cookie_login'] = True
                result['details'].append("Cookie登录: 跳过（未提供账号）")
            
            # 3. 登录状态检查
            login_success, login_msg = self.check_login_status(driver)
            result['login_status'] = login_success
            result['details'].append(f"登录状态: {login_msg}")
            
            # 4. 综合评估
            if network_success:
                result['success'] = True
                result['message'] = "网络问题解决成功，浏览器可正常使用"
                logger.info("✅ 网络问题解决成功")
            else:
                result['message'] = "部分问题已解决，但仍有网络连接问题"
                logger.warning("⚠️ 网络问题部分解决")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 网络问题解决流程异常: {e}")
            result['message'] = f"网络问题解决异常: {str(e)}"
            result['details'].append(f"异常: {str(e)}")
            return result
