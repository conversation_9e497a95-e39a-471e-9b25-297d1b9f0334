#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终网络速度解决方案
集成到现有系统的完整优化版本
"""

import sys
import time
import logging
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.enhanced_browser_manager import EnhancedBrowserManager
from src.core.network_issue_resolver import NetworkIssueResolver
from src.models.account import Account
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s'
)

logger = logging.getLogger(__name__)

class FinalNetworkSpeedSolution:
    """最终网络速度解决方案"""
    
    def __init__(self):
        self.cookies_dir = Path("data/cookies")
        # 浏览器实例池
        self.browser_pool = []
        self.max_pool_size = 3
        self.current_pool_index = 0  # 当前使用的浏览器索引
        
    def create_optimized_browser_pool(self):
        """创建优化的浏览器实例池"""
        logger.info("🏊 创建浏览器实例池...")
        
        for i in range(self.max_pool_size):
            try:
                options = self.create_ultra_optimized_options()
                service = Service(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=options)
                
                # 预热浏览器
                driver.get("about:blank")
                
                self.browser_pool.append({
                    'driver': driver,
                    'in_use': False,
                    'created_at': time.time()
                })
                
                logger.info(f"   ✅ 浏览器实例 {i+1} 创建完成")
                
            except Exception as e:
                logger.error(f"   ❌ 浏览器实例 {i+1} 创建失败: {e}")
        
        logger.info(f"🏊 浏览器池创建完成，共 {len(self.browser_pool)} 个实例")
    
    def get_browser_from_pool(self):
        """从池中轮换获取浏览器实例"""
        if not self.browser_pool:
            logger.warning("⚠️ 浏览器池为空，创建临时实例")
            options = self.create_ultra_optimized_options()
            service = Service(ChromeDriverManager().install())
            return webdriver.Chrome(service=service, options=options), -1

        # 轮换使用浏览器实例
        browser_info = self.browser_pool[self.current_pool_index]
        browser_info['in_use'] = True
        browser_info['last_used'] = time.time()

        current_index = self.current_pool_index

        # 更新索引到下一个浏览器
        self.current_pool_index = (self.current_pool_index + 1) % len(self.browser_pool)

        logger.info(f"🔄 使用浏览器实例 {current_index + 1}/{len(self.browser_pool)}")

        return browser_info['driver'], current_index
    
    def return_browser_to_pool(self, driver, browser_index):
        """将浏览器实例返回池中"""
        if browser_index == -1:
            # 临时创建的实例，直接关闭
            try:
                driver.quit()
            except:
                pass
            return

        if 0 <= browser_index < len(self.browser_pool):
            browser_info = self.browser_pool[browser_index]
            browser_info['in_use'] = False
            browser_info['return_time'] = time.time()

            # 清理浏览器状态
            try:
                driver.delete_all_cookies()
                driver.get("about:blank")
                logger.info(f"🔄 浏览器实例 {browser_index + 1} 已返回池中")
            except Exception as e:
                logger.warning(f"⚠️ 清理浏览器状态失败: {e}")
        else:
            logger.warning(f"⚠️ 无效的浏览器索引: {browser_index}")
    
    def create_ultra_optimized_options(self) -> ChromeOptions:
        """创建超优化的Chrome选项"""
        options = ChromeOptions()
        
        # 基于测试结果的最优配置
        ultra_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-software-rasterizer',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-hang-monitor',
            '--disable-prompt-on-repost',
            '--disable-domain-reliability',
            '--disable-component-update',
            '--disable-background-downloads',
            '--disable-client-side-phishing-detection',
            '--no-proxy-server',
            '--proxy-bypass-list=*',
            '--disable-proxy-certificate-handler',
            '--disable-background-networking',
            '--disable-default-apps',
            '--disable-sync',
            '--disable-translate',
            '--disable-plugins',
            '--disable-extensions',
            '--ignore-ssl-errors',
            '--ignore-certificate-errors',
            '--ignore-certificate-errors-spki-list',
            '--allow-running-insecure-content',
            '--disable-ssl-false-start',
            '--disable-web-security',
            '--memory-pressure-off',
            '--max_old_space_size=2048',
            '--disable-features=VizDisplayCompositor',
            '--disable-ipc-flooding-protection',
            '--disable-images',
            '--blink-settings=imagesEnabled=false',
            '--disable-java',
            '--disable-flash',
        ]
        
        for arg in ultra_args:
            options.add_argument(arg)
        
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        options.add_argument('--window-size=1280,720')
        
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        return options
    
    def ultra_fast_cookie_login(self, account_email: str) -> Dict:
        """超快速Cookie登录"""
        logger.info(f"⚡ 超快速Cookie登录: {account_email}")
        
        result = {
            'success': False,
            'message': '',
            'times': {},
            'login_indicators': []
        }
        
        start_total = time.time()
        
        try:
            # 1. 快速加载cookies
            start_time = time.time()
            cookies = self.load_cookies_ultra_fast(account_email)
            if not cookies:
                result['message'] = "无法加载cookies"
                return result
            
            result['times']['cookie_load'] = time.time() - start_time
            
            # 2. 从池中获取浏览器
            start_time = time.time()
            driver, browser_index = self.get_browser_from_pool()
            result['times']['browser_get'] = time.time() - start_time
            result['browser_index'] = browser_index
            
            # 3. 快速导航
            start_time = time.time()
            driver.set_page_load_timeout(3)
            driver.implicitly_wait(1)
            
            try:
                driver.get("https://mail.sina.com.cn/")
            except:
                pass  # 忽略超时
            
            result['times']['navigation'] = time.time() - start_time
            
            # 4. 应用cookies
            start_time = time.time()
            driver.delete_all_cookies()
            
            applied_count = 0
            for cookie in cookies:
                try:
                    clean_cookie = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie.get('domain', '.sina.com.cn'),
                        'path': cookie.get('path', '/'),
                    }
                    driver.add_cookie(clean_cookie)
                    applied_count += 1
                except:
                    pass
            
            result['times']['cookie_apply'] = time.time() - start_time
            
            # 5. 刷新并检查登录状态
            start_time = time.time()
            
            try:
                driver.refresh()
                time.sleep(1)  # 等待页面加载
                
                page_source = driver.page_source
                login_indicators = ['写信', '收件箱', '发件箱', '草稿箱', 'compose', 'inbox', '退出', 'logout']
                found_indicators = [indicator for indicator in login_indicators if indicator in page_source]
                
                result['success'] = len(found_indicators) > 0
                result['login_indicators'] = found_indicators
                result['current_url'] = driver.current_url
                result['page_title'] = driver.title
                
            except Exception as e:
                result['message'] = f"登录检查异常: {e}"
            
            result['times']['login_check'] = time.time() - start_time
            
            # 6. 返回浏览器到池中
            self.return_browser_to_pool(driver, browser_index)
            
            # 7. 计算总时间
            result['times']['total'] = time.time() - start_total
            
            if result['success']:
                result['message'] = f"登录成功，找到指示器: {result['login_indicators']}"
            else:
                result['message'] = "登录失败，未找到登录指示器"
            
            logger.info(f"   ⚡ 登录完成: {'成功' if result['success'] else '失败'} ({result['times']['total']:.3f}秒)")
            
            return result
            
        except Exception as e:
            result['times']['total'] = time.time() - start_total
            result['message'] = f"登录异常: {e}"
            logger.error(f"   ❌ 登录异常: {e}")
            return result
    
    def load_cookies_ultra_fast(self, account_email: str) -> Optional[List[Dict]]:
        """超快速加载cookies"""
        try:
            file_name = account_email.replace("@sina.com", "_sina_com.cookies")
            cookie_file = self.cookies_dir / file_name
            
            if not cookie_file.exists() or cookie_file.stat().st_size == 0:
                return None
            
            with open(cookie_file, 'r', encoding='utf-8') as f:
                file_content = f.read().strip()
            
            if not file_content:
                return None
            
            try:
                from src.utils.encryption import get_encryption_manager
                encryption_manager = get_encryption_manager()
                decrypted_content = encryption_manager.decrypt(file_content)
                if decrypted_content:
                    cookies_data = json.loads(decrypted_content)
                else:
                    cookies_data = json.loads(file_content)
            except:
                cookies_data = json.loads(file_content)
            
            if isinstance(cookies_data, dict) and 'cookies' in cookies_data:
                cookies = cookies_data['cookies']
            elif isinstance(cookies_data, list):
                cookies = cookies_data
            else:
                return None
            
            return cookies
            
        except Exception as e:
            logger.error(f"❌ Cookie加载失败: {account_email} - {e}")
            return None
    
    def test_all_accounts(self):
        """测试所有可用账号"""
        print("⚡ 最终网络速度解决方案测试")
        print("="*80)
        
        # 创建浏览器池
        self.create_optimized_browser_pool()
        
        # 获取可用账号
        accounts = []
        if self.cookies_dir.exists():
            for cookie_file in self.cookies_dir.glob("*.cookies"):
                account_name = cookie_file.stem.replace("_sina_com", "@sina.com")
                accounts.append(account_name)
        
        if not accounts:
            print("❌ 未找到可用的Cookie文件")
            return
        
        print(f"📋 找到 {len(accounts)} 个可用账号，测试前5个:")
        
        results = []
        
        # 测试前5个账号
        for i, account in enumerate(accounts[:5], 1):
            print(f"\n⚡ 最终测试 {i}/5: {account}")
            print("-" * 50)
            
            result = self.ultra_fast_cookie_login(account)
            
            # 显示结果
            browser_info = f"浏览器实例 {result.get('browser_index', -1) + 1}" if result.get('browser_index', -1) >= 0 else "临时实例"
            print(f"   结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
            print(f"   使用: {browser_info}")
            print(f"   消息: {result['message']}")
            print(f"   总耗时: {result['times'].get('total', 0):.3f}秒")
            
            if result['times']:
                print(f"   时间分解:")
                for phase, duration in result['times'].items():
                    if phase != 'total':
                        print(f"      {phase}: {duration:.3f}秒")
            
            results.append(result)
        
        # 性能分析
        self.analyze_final_performance(results)
        
        # 清理浏览器池
        self.cleanup_browser_pool()
    
    def analyze_final_performance(self, results: List[Dict]):
        """分析最终性能"""
        print(f"\n⚡ 最终性能分析")
        print("="*80)
        
        if not results:
            return
        
        successful = [r for r in results if r['success']]
        success_rate = len(successful) / len(results) * 100
        
        print(f"📊 最终统计:")
        print(f"   测试账号数: {len(results)}")
        print(f"   成功率: {success_rate:.1f}% ({len(successful)}/{len(results)})")
        
        if results:
            total_times = [r['times'].get('total', 0) for r in results if 'total' in r['times']]
            if total_times:
                avg_total = sum(total_times) / len(total_times)
                min_total = min(total_times)
                max_total = max(total_times)
                
                print(f"\n⚡ 速度统计:")
                print(f"   平均耗时: {avg_total:.3f}秒")
                print(f"   最快耗时: {min_total:.3f}秒")
                print(f"   最慢耗时: {max_total:.3f}秒")
                
                # 与原始方案对比
                original_time = 11.82
                improvement = ((original_time - avg_total) / original_time) * 100
                
                print(f"\n🚀 性能改进:")
                print(f"   相比原始方案提升: {improvement:.1f}%")
                print(f"   平均节省时间: {original_time - avg_total:.3f}秒")
                
                if avg_total < 3:
                    print("   🎉 性能优秀！已达到3秒以内")
                elif avg_total < 5:
                    print("   ✅ 性能良好！已达到5秒以内")
                elif avg_total < 10:
                    print("   👍 性能可接受！已达到10秒以内")
                else:
                    print("   ⚠️ 仍需进一步优化")
    
    def cleanup_browser_pool(self):
        """清理浏览器池"""
        logger.info("🧹 清理浏览器池...")
        
        for browser_info in self.browser_pool:
            try:
                browser_info['driver'].quit()
            except:
                pass
        
        self.browser_pool.clear()
        logger.info("✅ 浏览器池清理完成")

if __name__ == "__main__":
    print("⚡ 启动最终网络速度解决方案测试...")
    
    solution = FinalNetworkSpeedSolution()
    solution.test_all_accounts()
    
    print("\n✅ 最终测试完成！")
