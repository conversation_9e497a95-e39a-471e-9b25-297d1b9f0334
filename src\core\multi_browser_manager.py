#!/usr/bin/env python3
"""
多浏览器管理器
管理多个浏览器窗口，支持Cookie复用和代理IP轮换
"""

import time
import threading
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from src.utils.logger import setup_logger
from src.models.account import Account
from src.models.browser_instance import BrowserInstance
from src.core.cookie_manager import CookieManager
from src.core.proxy_manager import ProxyManager

logger = setup_logger("INFO")

@dataclass
class SendingConfig:
    """发送配置"""
    max_browsers: int = 3
    emails_per_account: int = 5
    send_interval: float = 2.0  # 秒
    browser_window_width: int = 800
    browser_window_height: int = 600
    minimize_browsers: bool = True
    rotate_accounts: bool = True
    max_retries: int = 3

class MultiBrowserManager:
    """多浏览器管理器"""
    
    def __init__(self, config: SendingConfig):
        self.config = config
        self.browsers: Dict[str, BrowserInstance] = {}
        self.account_queue: List[Account] = []
        self.current_account_index = 0
        self.send_lock = threading.Lock()
        self.is_running = False
        
        # 初始化管理器
        self.cookie_manager = CookieManager({
            'cookie_dir': 'data/cookies',
            'encryption_enabled': True,
            'max_age_days': 30
        })
        self.proxy_manager = ProxyManager()

        # 初始化无缝账号切换器
        from .seamless_account_switcher import SeamlessAccountSwitcher
        self.seamless_switcher = SeamlessAccountSwitcher(
            self.cookie_manager,
            self.proxy_manager
        )

        # 账号切换统计
        self.account_switch_stats = {
            'total_switches': 0,
            'seamless_switches': 0,
            'traditional_switches': 0,
            'failed_switches': 0,
            'avg_switch_time': 0.0
        }
        
        logger.info(f"🚀 多浏览器管理器初始化完成")
        logger.info(f"📊 配置: {self.config.max_browsers}个浏览器, 每账号{self.config.emails_per_account}封邮件")
    
    def create_browser_instance(self, browser_id: str, proxy_info: Optional[Dict] = None) -> Optional[BrowserInstance]:
        """创建浏览器实例"""
        try:
            logger.info(f"🌐 创建浏览器实例: {browser_id}")
            
            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')
            chrome_options.add_argument('--disable-javascript')
            
            # 设置代理
            if proxy_info:
                proxy_url = f"{proxy_info['ip']}:{proxy_info['port']}"
                chrome_options.add_argument(f'--proxy-server=http://{proxy_url}')
                logger.info(f"🌐 浏览器 {browser_id} 使用代理: {proxy_url}")
            else:
                logger.info(f"🌐 浏览器 {browser_id} 使用直连模式")
            
            # 设置窗口大小
            if self.config.minimize_browsers:
                chrome_options.add_argument('--window-size=400,300')
                chrome_options.add_argument('--window-position=0,0')
            else:
                chrome_options.add_argument(f'--window-size={self.config.browser_window_width},{self.config.browser_window_height}')
            
            # 创建WebDriver
            service = Service()
            driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 设置窗口属性
            if self.config.minimize_browsers:
                driver.minimize_window()
                is_minimized = True
            else:
                driver.set_window_size(self.config.browser_window_width, self.config.browser_window_height)
                is_minimized = False
            
            # 创建浏览器实例
            browser_instance = BrowserInstance(
                browser_id=browser_id,
                driver=driver,
                proxy_info=proxy_info,
                current_account=None,
                window_size=(self.config.browser_window_width, self.config.browser_window_height),
                is_minimized=is_minimized
            )
            
            logger.info(f"✅ 浏览器实例创建成功: {browser_id}")
            return browser_instance
            
        except Exception as e:
            logger.error(f"❌ 创建浏览器实例失败: {browser_id} - {e}")
            return None
    
    def initialize_browsers(self) -> bool:
        """初始化所有浏览器实例"""
        try:
            logger.info(f"🚀 初始化 {self.config.max_browsers} 个浏览器实例...")

            if self.config.max_browsers <= 0:
                logger.error(f"❌ 配置的浏览器数量无效: {self.config.max_browsers}")
                return False

            for i in range(self.config.max_browsers):
                browser_id = f"browser_{i+1}"
                
                # 获取代理IP
                proxy_info = None
                if self.proxy_manager:
                    proxy_id = self.proxy_manager.select_best_proxy_for_account(f"browser_{i+1}")
                    if proxy_id:
                        proxy_info = self.proxy_manager.get_proxy_info(proxy_id)
                
                # 创建浏览器实例
                browser_instance = self.create_browser_instance(browser_id, proxy_info)
                if browser_instance:
                    self.browsers[browser_id] = browser_instance
                    logger.info(f"✅ 浏览器 {browser_id} 初始化成功")
                else:
                    logger.error(f"❌ 浏览器 {browser_id} 初始化失败")
            
            success_count = len(self.browsers)

            # 超极速优化：初始化超极速Cookie管理器
            if success_count > 0:
                try:
                    from src.core.ultra_speed_cookie_manager import UltraSpeedCookieManager
                    self.ultra_cookie_manager = UltraSpeedCookieManager(self.cookie_manager)
                    logger.info("⚡ 超极速Cookie管理器已初始化")
                except Exception as e:
                    logger.warning(f"⚠️ 超极速Cookie管理器初始化失败: {e}")

            logger.info(f"📊 浏览器初始化完成: {success_count}/{self.config.max_browsers} 成功")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"❌ 浏览器初始化失败: {e}")
            return False
    
    def load_account_cookies(self, browser_instance: BrowserInstance, account: Account) -> bool:
        """为浏览器实例加载账号Cookie - 超极速优化版本"""
        try:
            logger.info(f"⚡ 超极速加载Cookie: {browser_instance.browser_id} -> {account.email}")

            # 超极速优化：使用超极速Cookie管理器
            if not hasattr(self, 'ultra_cookie_manager'):
                from src.core.ultra_speed_cookie_manager import UltraSpeedCookieManager
                self.ultra_cookie_manager = UltraSpeedCookieManager(self.cookie_manager)

            # 超极速Cookie登录
            success = self.ultra_cookie_manager.ultra_speed_cookie_login(
                browser_instance.driver,
                account.email
            )

            if success:
                # 设置账号信息
                browser_instance.current_account = account
                browser_instance.status = "ready"
                browser_instance.last_activity = time.time()

                logger.info(f"⚡ 超极速Cookie登录成功: {account.email}")
                return True
            else:
                logger.error(f"❌ 超极速Cookie登录失败: {account.email}")
                return False



        except Exception as e:
            logger.error(f"❌ 加载Cookie失败: {account.email} - {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def _verify_login_status(self, driver: webdriver.Chrome) -> bool:
        """验证登录状态"""
        try:
            # 获取当前URL和页面内容
            current_url = driver.current_url.lower()
            page_source = driver.page_source.lower()

            logger.debug(f"🔍 验证登录状态 - URL: {current_url}")

            # 检查URL是否表示登录成功
            success_urls = [
                'mail.sina.com.cn/classic',
                'mail.sina.com.cn/#',
                'm0.mail.sina.com.cn/classic',
                'm1.mail.sina.com.cn/classic',
                'm2.mail.sina.com.cn/classic'
            ]

            url_success = any(url in current_url for url in success_urls)

            # 检查页面内容中的成功标识
            success_indicators = [
                'inbox', 'mailbox', '写邮件', '通讯录', 'contacts',
                '设置', 'settings', '收件箱', '发件箱', 'compose'
            ]

            # 检查登录失败标识
            login_indicators = [
                '登录', 'login', '用户名', 'username', '密码', 'password',
                '验证码', 'captcha', '立即登录'
            ]

            success_count = sum(1 for indicator in success_indicators if indicator in page_source)
            login_count = sum(1 for indicator in login_indicators if indicator in page_source)

            logger.debug(f"📊 验证结果 - URL成功: {url_success}, 成功指标: {success_count}, 登录指标: {login_count}")

            # 判断登录状态
            if url_success and success_count >= 3:
                logger.debug("✅ 登录状态验证成功 (URL + 内容)")
                return True
            elif success_count >= 5 and login_count <= 2:
                logger.debug("✅ 登录状态验证成功 (内容)")
                return True
            elif url_success and login_count <= 3:
                logger.debug("✅ 登录状态验证成功 (URL)")
                return True
            else:
                logger.debug(f"❌ 登录状态验证失败 - 成功指标不足或登录指标过多")
                return False

        except Exception as e:
            logger.error(f"❌ 验证登录状态失败: {e}")
            return False
    
    def set_account_queue(self, accounts: List[Account]):
        """设置账号队列"""
        # 过滤掉冷却中的账号
        available_accounts = [acc for acc in accounts if acc.is_available_for_sending()]
        self.account_queue = available_accounts.copy()
        self.current_account_index = 0

        filtered_count = len(accounts) - len(available_accounts)
        if filtered_count > 0:
            logger.info(f"📋 过滤掉 {filtered_count} 个冷却中的账号")
        logger.info(f"📋 设置账号队列: {len(available_accounts)} 个可用账号")
    
    def get_next_account(self) -> Optional[Account]:
        """获取下一个账号"""
        if not self.account_queue:
            return None
        
        if self.current_account_index >= len(self.account_queue):
            if self.config.rotate_accounts:
                self.current_account_index = 0
                logger.info("🔄 账号队列轮换，重新开始")
            else:
                logger.info("📋 账号队列已用完")
                return None
        
        account = self.account_queue[self.current_account_index]
        self.current_account_index += 1
        return account
    
    def get_available_browser(self) -> Optional[BrowserInstance]:
        """获取可用的浏览器实例"""
        for browser_instance in self.browsers.values():
            if browser_instance.status in ["idle", "ready"]:
                return browser_instance
        return None
    
    def switch_account_if_needed(self, browser_instance: BrowserInstance, force_switch: bool = False) -> bool:
        """如果需要，无缝切换账号 - 使用Cookie和代理切换"""
        # 检查是否需要切换账号
        should_switch = (
            force_switch or  # 强制切换
            (browser_instance.current_account and
             browser_instance.sent_count >= self.config.emails_per_account)
        )

        if should_switch:
            if force_switch:
                logger.info(f"🔄 强制切换账号: {browser_instance.browser_id}")
            else:
                logger.info(f"🔄 浏览器 {browser_instance.browser_id} 需要切换账号 (已发送 {browser_instance.sent_count} 封)")

            # 获取下一个账号
            next_account = self.get_next_account()
            if next_account:
                # 使用无缝账号切换器进行切换
                return self.seamless_switch_account(browser_instance, next_account)
            else:
                logger.warning(f"⚠️ 没有更多账号可用于浏览器 {browser_instance.browser_id}")
                return False

        return True

    def seamless_switch_account(self, browser_instance: BrowserInstance, new_account: Account) -> bool:
        """无缝切换账号 - 通过Cookie和代理切换，无需点击退出"""
        try:
            logger.info(f"🔄 开始无缝切换账号: {browser_instance.browser_id} -> {new_account.email}")

            # 检查是否可以进行无缝切换
            if not self.seamless_switcher.can_switch_seamlessly(browser_instance, new_account):
                logger.warning(f"⚠️ 无法进行无缝切换，回退到传统方式: {new_account.email}")
                return self.load_account_cookies(browser_instance, new_account)

            # 执行无缝切换
            switch_result = self.seamless_switcher.seamless_switch_account(
                browser_instance,
                new_account,
                switch_proxy=True
            )

            if switch_result.success:
                logger.info(f"✅ 无缝账号切换成功: {switch_result.old_account} -> {switch_result.new_account} ({switch_result.switch_time:.2f}s)")

                # 记录切换统计
                self._record_account_switch(browser_instance, switch_result)

                return True
            else:
                logger.error(f"❌ 无缝账号切换失败: {switch_result.error_message}")

                # 尝试传统切换方式作为备用
                logger.info("🔄 尝试传统账号切换方式...")
                return self.load_account_cookies(browser_instance, new_account)

        except Exception as e:
            logger.error(f"❌ 无缝账号切换异常: {e}")

            # 回退到传统方式
            logger.info("🔄 回退到传统账号切换方式...")
            return self.load_account_cookies(browser_instance, new_account)

    def _record_account_switch(self, browser_instance: BrowserInstance, switch_result):
        """记录账号切换统计"""
        self.account_switch_stats['total_switches'] += 1

        if switch_result.success:
            self.account_switch_stats['seamless_switches'] += 1

            # 更新平均切换时间
            total_switches = self.account_switch_stats['seamless_switches']
            current_avg = self.account_switch_stats['avg_switch_time']
            self.account_switch_stats['avg_switch_time'] = (
                (current_avg * (total_switches - 1) + switch_result.switch_time) / total_switches
            )
        else:
            self.account_switch_stats['failed_switches'] += 1

        logger.debug(f"📊 账号切换统计: {self.account_switch_stats}")

    def get_account_switch_stats(self) -> dict:
        """获取账号切换统计"""
        stats = self.account_switch_stats.copy()

        # 添加无缝切换器的统计
        seamless_stats = self.seamless_switcher.get_switch_stats()
        stats['seamless_switcher_stats'] = seamless_stats

        return stats

    def force_switch_account(self, browser_instance: BrowserInstance, new_account: Account) -> bool:
        """强制切换账号 - 用于手动切换"""
        logger.info(f"🔄 强制切换账号: {browser_instance.browser_id} -> {new_account.email}")

        # 重置发送计数，强制触发切换
        old_count = browser_instance.sent_count
        browser_instance.sent_count = self.config.emails_per_account

        # 执行切换
        result = self.seamless_switch_account(browser_instance, new_account)

        # 如果切换失败，恢复原来的计数
        if not result:
            browser_instance.sent_count = old_count

        return result
    
    def cleanup_browsers(self):
        """清理所有浏览器实例"""
        logger.info("🧹 开始清理浏览器实例...")
        
        for browser_id, browser_instance in self.browsers.items():
            try:
                browser_instance.driver.quit()
                logger.info(f"✅ 浏览器 {browser_id} 已关闭")
            except Exception as e:
                logger.error(f"❌ 关闭浏览器 {browser_id} 失败: {e}")
        
        self.browsers.clear()
        logger.info("✅ 所有浏览器实例已清理")
    
    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        total_browsers = len(self.browsers)
        active_browsers = sum(1 for b in self.browsers.values() if b.status in ["ready", "busy"])
        total_sent = sum(b.sent_count for b in self.browsers.values())
        
        return {
            'total_browsers': total_browsers,
            'active_browsers': active_browsers,
            'total_sent': total_sent,
            'account_queue_remaining': len(self.account_queue) - self.current_account_index,
            'browsers_detail': {
                browser_id: {
                    'status': browser.status,
                    'current_account': browser.current_account.email if browser.current_account else None,
                    'sent_count': browser.sent_count,
                    'is_minimized': browser.is_minimized
                }
                for browser_id, browser in self.browsers.items()
            }
        }
