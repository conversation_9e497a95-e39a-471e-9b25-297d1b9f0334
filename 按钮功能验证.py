#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发送控制按钮功能验证脚本
"""

import sys
import os
sys.path.insert(0, '.')

def verify_button_methods():
    """验证按钮方法是否存在"""
    print("🔧 验证发送控制按钮方法...")
    
    try:
        from src.gui.optimized_multi_browser_widget import OptimizedMultiBrowserWidget
        import inspect
        
        # 获取类的所有方法
        methods = inspect.getmembers(OptimizedMultiBrowserWidget, predicate=inspect.isfunction)
        method_names = [name for name, _ in methods]
        
        # 检查四个关键方法
        required_methods = {
            "start_sender": "启动发送器",
            "stop_sending": "停止发送",
            "pause_sending": "暂停发送",
            "resume_sending": "恢复发送"
        }
        
        print("📋 发送控制方法检查:")
        all_exist = True
        for method_name, description in required_methods.items():
            exists = method_name in method_names
            status = "✅" if exists else "❌"
            print(f"   {status} {method_name}: {description} - {'已实现' if exists else '未实现'}")
            if not exists:
                all_exist = False
        
        # 检查方法的具体实现
        if all_exist:
            print("\n📋 方法实现检查:")
            for method_name, description in required_methods.items():
                try:
                    method = getattr(OptimizedMultiBrowserWidget, method_name)
                    source = inspect.getsource(method)
                    
                    # 检查方法是否有实际实现（不只是pass）
                    has_implementation = len(source.strip().split('\n')) > 2 and 'pass' not in source
                    status = "✅" if has_implementation else "⚠️"
                    impl_status = "有实现" if has_implementation else "仅声明"
                    print(f"   {status} {method_name}: {impl_status}")
                    
                except Exception as e:
                    print(f"   ❌ {method_name}: 检查失败 - {e}")
        
        return all_exist
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_ui_elements():
    """验证UI元素是否正确创建"""
    print("\n🔧 验证UI元素创建...")
    
    try:
        from src.gui.optimized_multi_browser_widget import OptimizedMultiBrowserWidget
        import inspect
        
        # 检查create_send_control_group方法
        source = inspect.getsource(OptimizedMultiBrowserWidget.create_send_control_group)
        
        # 检查四个按钮是否在方法中创建
        buttons = {
            "start_sender_btn": "启动按钮",
            "stop_btn": "停止按钮", 
            "pause_btn": "暂停按钮",
            "resume_btn": "恢复按钮"
        }
        
        print("📋 UI按钮创建检查:")
        all_created = True
        for btn_name, description in buttons.items():
            created = btn_name in source
            status = "✅" if created else "❌"
            print(f"   {status} {btn_name}: {description} - {'已创建' if created else '未创建'}")
            if not created:
                all_created = False
        
        # 检查按钮样式
        print("\n📋 按钮样式检查:")
        style_checks = {
            "网格布局": "QGridLayout" in source,
            "统一样式": "button_style_base" in source,
            "颜色区分": "#27ae60" in source and "#e74c3c" in source and "#f39c12" in source and "#3498db" in source,
            "点击事件": "clicked.connect" in source
        }
        
        for check_name, passed in style_checks.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}: {'已实现' if passed else '未实现'}")
        
        return all_created and all(style_checks.values())
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_account_features():
    """验证账号管理功能"""
    print("\n🔧 验证账号管理功能...")
    
    try:
        from src.models.account import AccountManager
        from src.models.database import DatabaseManager
        from src.utils.config_manager import ConfigManager
        
        # 创建管理器
        config = ConfigManager()
        config.load_config()
        db_path = config.get('database.path', 'data/sina_email.db')
        db_manager = DatabaseManager(db_path)
        account_manager = AccountManager(db_manager)
        
        # 测试关键功能
        print("📋 账号管理功能测试:")
        
        # 1. 获取账号列表
        try:
            accounts = account_manager.get_all_accounts()
            print(f"   ✅ 获取账号列表: 成功 ({len(accounts)} 个账号)")
        except Exception as e:
            print(f"   ❌ 获取账号列表: 失败 - {e}")
            return False
        
        # 2. 测试发送次数更新（如果有账号的话）
        if accounts:
            test_account = accounts[0]
            try:
                original_count = test_account.send_count if hasattr(test_account, 'send_count') else 0
                account_manager.increment_send_count(test_account.id)
                
                # 重新获取账号验证更新
                updated_account = account_manager.get_account_by_id(test_account.id)
                new_count = updated_account.send_count if hasattr(updated_account, 'send_count') else 0
                
                if new_count > original_count:
                    print(f"   ✅ 发送次数更新: 成功 ({original_count} → {new_count})")
                    
                    # 恢复原始值
                    account_manager.update_send_count(test_account.id, original_count)
                else:
                    print(f"   ⚠️ 发送次数更新: 数值未变化")
                    
            except Exception as e:
                print(f"   ❌ 发送次数更新: 失败 - {e}")
        
        # 3. 测试最后使用时间更新
        if accounts:
            test_account = accounts[0]
            try:
                account_manager.update_last_used(test_account.id)
                print(f"   ✅ 最后使用时间更新: 成功")
            except Exception as e:
                print(f"   ❌ 最后使用时间更新: 失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 发送控制按钮和账号管理功能验证")
    print("=" * 60)
    
    # 执行验证
    tests = [
        ("按钮方法验证", verify_button_methods),
        ("UI元素验证", verify_ui_elements),
        ("账号管理验证", verify_account_features)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 验证结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项验证通过")
    
    if passed == total:
        print("🎉 所有功能验证通过！")
        print("\n📋 功能确认:")
        print("   ✅ 四个发送控制按钮已正确实现")
        print("   ✅ 按钮样式统一美观")
        print("   ✅ 账号发送次数和最后使用时间功能完整")
        print("   ✅ 数据库更新机制正常工作")
    else:
        print("⚠️ 部分功能需要检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
