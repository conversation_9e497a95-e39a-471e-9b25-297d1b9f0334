#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网络问题修复测试
基于项目成功经验，验证新浪邮箱网络问题解决方案
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.enhanced_browser_manager import EnhancedBrowserManager
from src.core.network_issue_resolver import NetworkIssueResolver
from src.models.account import Account

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('network_fix_test.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

class NetworkIssueFixTest:
    """网络问题修复测试器"""
    
    def __init__(self):
        self.config = {
            'browser_config': {
                'window_size': [1280, 720],
                'window_mode': '正常窗口',
                'headless': False,
                'disable_images': True,
                'user_data_dir': 'temp_test_browser'
            }
        }
        
        # 创建测试账号
        self.test_account = Account(
            email="<EMAIL>",
            password="test123"
        )
    
    def test_network_resolver_only(self):
        """测试网络问题解决器（独立测试）"""
        print("\n" + "="*80)
        print("🔧 测试1: 网络问题解决器独立测试")
        print("="*80)
        
        try:
            resolver = NetworkIssueResolver()
            
            # 创建优化的Chrome选项
            print("📋 创建优化Chrome选项...")
            options = resolver.create_optimized_chrome_options()
            print(f"   ✅ Chrome选项创建成功，包含 {len(options.arguments)} 个参数")
            
            # 显示关键配置
            key_args = [arg for arg in options.arguments if any(keyword in arg for keyword in 
                       ['proxy', 'ssl', 'certificate', 'security', 'network'])]
            print(f"   🔧 关键网络修复参数: {len(key_args)} 个")
            for arg in key_args[:5]:  # 显示前5个
                print(f"      - {arg}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 网络解决器测试失败: {e}")
            return False
    
    def test_enhanced_browser_creation(self):
        """测试增强版浏览器创建"""
        print("\n" + "="*80)
        print("🚀 测试2: 增强版浏览器创建测试")
        print("="*80)
        
        driver = None
        try:
            # 创建增强版浏览器管理器
            manager = EnhancedBrowserManager(self.config)
            
            print(f"🔧 创建新浪邮箱专用浏览器: {self.test_account.email}")
            
            # 创建浏览器
            start_time = time.time()
            driver, result = manager.create_sina_mail_browser(self.test_account)
            elapsed = time.time() - start_time
            
            print(f"⏱️ 浏览器创建耗时: {elapsed:.2f}秒")
            print(f"📊 创建结果: {'成功' if result['success'] else '失败'}")
            print(f"💬 结果消息: {result['message']}")
            
            # 显示详细信息
            print("\n📋 详细信息:")
            for i, detail in enumerate(result['details'], 1):
                print(f"   {i}. {detail}")
            
            # 显示状态
            print(f"\n📈 状态统计:")
            print(f"   🌐 网络解决: {'✅' if result['network_resolved'] else '❌'}")
            print(f"   🍪 Cookie应用: {'✅' if result['cookie_applied'] else '❌'}")
            print(f"   🔐 登录确认: {'✅' if result['login_confirmed'] else '❌'}")
            
            if driver:
                print(f"\n🌐 浏览器信息:")
                try:
                    print(f"   当前URL: {driver.current_url}")
                    print(f"   页面标题: {driver.title}")
                    print(f"   会话ID: {driver.session_id[:20]}...")
                except Exception as e:
                    print(f"   ⚠️ 获取浏览器信息失败: {e}")
                
                return True, driver
            else:
                print("❌ 浏览器创建失败")
                return False, None
                
        except Exception as e:
            print(f"❌ 增强版浏览器测试异常: {e}")
            return False, None
    
    def test_browser_functionality(self, driver):
        """测试浏览器功能"""
        print("\n" + "="*80)
        print("🧪 测试3: 浏览器功能测试")
        print("="*80)
        
        try:
            manager = EnhancedBrowserManager(self.config)
            
            # 执行功能测试
            test_result = manager.test_browser_functionality(driver)
            
            print(f"📊 测试结果: {'通过' if test_result['success'] else '失败'}")
            print(f"📈 通过率: {test_result['tests_passed']}/{test_result['total_tests']} ({test_result['tests_passed']/test_result['total_tests']*100:.1f}%)")
            
            print("\n📋 测试详情:")
            for i, detail in enumerate(test_result['details'], 1):
                print(f"   {i}. {detail}")
            
            return test_result['success']
            
        except Exception as e:
            print(f"❌ 浏览器功能测试异常: {e}")
            return False
    
    def test_network_connectivity_fix(self, driver):
        """测试网络连接修复"""
        print("\n" + "="*80)
        print("🌐 测试4: 网络连接修复验证")
        print("="*80)
        
        try:
            resolver = NetworkIssueResolver()
            
            # 测试网络连接
            print("🔍 测试网络连接性...")
            network_success, network_msg = resolver.test_network_connectivity(driver)
            
            print(f"📊 网络测试结果: {'成功' if network_success else '失败'}")
            print(f"💬 网络消息: {network_msg}")
            
            if network_success:
                # 测试页面交互
                print("\n🖱️ 测试页面交互...")
                try:
                    # 尝试获取页面元素
                    page_text = driver.execute_script("return document.body.innerText;")
                    if page_text:
                        print(f"   ✅ 页面内容获取成功 ({len(page_text)} 字符)")
                        
                        # 检查关键词
                        keywords = ["新浪", "邮箱", "登录", "sina", "mail"]
                        found_keywords = [kw for kw in keywords if kw in page_text]
                        print(f"   🔍 找到关键词: {found_keywords}")
                    else:
                        print("   ⚠️ 页面内容为空")
                        
                except Exception as e:
                    print(f"   ❌ 页面交互测试失败: {e}")
            
            return network_success
            
        except Exception as e:
            print(f"❌ 网络连接测试异常: {e}")
            return False
    
    def run_complete_test(self):
        """运行完整测试"""
        print("🎯 新浪邮箱网络问题修复完整测试")
        print("="*80)
        print("🎯 测试目标:")
        print("   1. 验证网络问题解决器配置")
        print("   2. 验证增强版浏览器创建")
        print("   3. 验证浏览器功能正常")
        print("   4. 验证网络连接修复效果")
        print("="*80)
        
        results = {
            'resolver_test': False,
            'browser_creation': False,
            'browser_functionality': False,
            'network_connectivity': False
        }
        
        driver = None
        
        try:
            # 测试1: 网络解决器
            results['resolver_test'] = self.test_network_resolver_only()
            
            # 测试2: 浏览器创建
            browser_success, driver = self.test_enhanced_browser_creation()
            results['browser_creation'] = browser_success
            
            if driver:
                # 测试3: 浏览器功能
                results['browser_functionality'] = self.test_browser_functionality(driver)
                
                # 测试4: 网络连接
                results['network_connectivity'] = self.test_network_connectivity_fix(driver)
            
            # 显示最终结果
            self.show_final_results(results)
            
        except Exception as e:
            print(f"❌ 完整测试异常: {e}")
            
        finally:
            # 清理资源
            if driver:
                try:
                    print("\n🧹 清理浏览器资源...")
                    driver.quit()
                    print("   ✅ 浏览器资源清理完成")
                except Exception as e:
                    print(f"   ⚠️ 浏览器清理失败: {e}")
    
    def show_final_results(self, results):
        """显示最终结果"""
        print("\n" + "="*80)
        print("📊 最终测试结果")
        print("="*80)
        
        total_tests = len(results)
        passed_tests = sum(results.values())
        
        print(f"📈 总体通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
        print()
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            test_display = {
                'resolver_test': '网络解决器测试',
                'browser_creation': '浏览器创建测试',
                'browser_functionality': '浏览器功能测试',
                'network_connectivity': '网络连接测试'
            }
            print(f"{status} {test_display[test_name]}")
        
        print("\n" + "="*80)
        
        if passed_tests >= 3:
            print("🎉 测试基本通过！网络问题修复方案有效")
            print("💡 建议：可以将此方案应用到生产环境")
        elif passed_tests >= 2:
            print("⚠️ 测试部分通过，需要进一步优化")
            print("💡 建议：检查失败的测试项并进行针对性修复")
        else:
            print("❌ 测试失败较多，需要重新检查配置")
            print("💡 建议：检查网络环境和Chrome驱动配置")

if __name__ == "__main__":
    print("🚀 启动网络问题修复测试...")
    
    tester = NetworkIssueFixTest()
    tester.run_complete_test()
    
    print("\n✅ 测试完成！")
    print("📝 详细日志已保存到: network_fix_test.log")
