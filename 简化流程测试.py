#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化流程测试脚本
专注测试Cookie登录和邮件发送功能
发送测试邮件到 <EMAIL>
"""

import sys
import os
import time
sys.path.insert(0, '.')

def test_cookie_system():
    """测试Cookie系统"""
    print("🔧 测试Cookie系统...")
    
    try:
        from src.core.cookie_manager import CookieManager
        from src.utils.config_manager import ConfigManager
        
        # 加载配置
        config_manager = ConfigManager()
        config_manager.load_config()
        config = getattr(config_manager, 'config', {})
        
        # 创建Cookie管理器
        cookie_manager = CookieManager(config)
        print("   ✅ Cookie管理器创建成功")
        
        # 测试加载Cookie
        test_accounts = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        
        for account_email in test_accounts:
            cookie_data = cookie_manager.get_cookies(account_email)
            if cookie_data and 'cookies' in cookie_data:
                cookies = cookie_data['cookies']
                print(f"   ✅ {account_email}: {len(cookies)} 个Cookie")
                return True, account_email, cookies
            else:
                print(f"   ⚠️ {account_email}: 没有找到Cookie")
        
        return False, None, None
        
    except Exception as e:
        print(f"   ❌ Cookie系统测试失败: {e}")
        return False, None, None

def test_browser_with_existing_system():
    """使用现有系统测试浏览器"""
    print("\n🚀 使用现有系统测试浏览器...")
    
    try:
        from src.gui.optimized_multi_browser_widget import OptimizedMultiBrowserWidget
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        from PyQt5.QtWidgets import QApplication
        import sys
        
        # 创建QApplication（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        # 创建多浏览器组件
        browser_widget = OptimizedMultiBrowserWidget(db_manager, config)
        print("   ✅ 多浏览器组件创建成功")
        
        # 设置浏览器数量为1
        browser_widget.browser_count_spin.setValue(1)
        
        # 启动发送器（这会创建浏览器）
        print("   🚀 启动发送器...")
        browser_widget._start_concurrent_sending()
        
        # 等待浏览器创建
        time.sleep(10)
        
        # 检查浏览器状态
        if hasattr(browser_widget, 'browser_drivers') and browser_widget.browser_drivers:
            driver = browser_widget.browser_drivers[0]
            print("   ✅ 浏览器创建成功")
            
            # 检查当前状态
            try:
                current_url = driver.current_url
                page_title = driver.title
                print(f"   📊 当前URL: {current_url}")
                print(f"   📊 页面标题: {page_title}")
                
                # 判断是否在新浪邮箱
                if "mail.sina.com.cn" in current_url:
                    print("   ✅ 成功导航到新浪邮箱")
                    return True, driver
                else:
                    print("   ❌ 未导航到新浪邮箱")
                    return False, None
                    
            except Exception as e:
                print(f"   ❌ 检查浏览器状态失败: {e}")
                return False, None
        else:
            print("   ❌ 浏览器创建失败")
            return False, None
            
    except Exception as e:
        print(f"   ❌ 浏览器测试失败: {e}")
        return False, None

def test_email_sending_simple(driver):
    """简化的邮件发送测试"""
    print("\n📧 测试邮件发送...")
    
    try:
        from selenium.webdriver.common.by import By
        import time
        
        print("   🔍 查找写信功能...")
        
        # 等待页面加载
        time.sleep(3)
        
        # 尝试查找写信按钮
        write_selectors = [
            "//a[contains(text(), '写信')]",
            "//a[contains(@href, 'compose')]",
            "//*[contains(@class, 'compose')]",
            "//button[contains(text(), '写信')]"
        ]
        
        write_button = None
        for selector in write_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements and elements[0].is_displayed():
                    write_button = elements[0]
                    print(f"   ✅ 找到写信按钮: {selector}")
                    break
            except:
                continue
        
        if not write_button:
            print("   ❌ 未找到写信按钮")
            # 尝试直接访问写信页面
            print("   🔄 尝试直接访问写信页面...")
            driver.get("https://mail.sina.com.cn/compose/")
            time.sleep(3)
        else:
            # 点击写信按钮
            print("   🖱️ 点击写信按钮...")
            driver.execute_script("arguments[0].click();", write_button)
            time.sleep(3)
        
        # 检查是否进入写信页面
        current_url = driver.current_url
        print(f"   📊 当前URL: {current_url}")
        
        if "compose" in current_url.lower() or "write" in current_url.lower():
            print("   ✅ 成功进入写信页面")
            
            # 查找邮件表单
            print("   📝 查找邮件表单...")
            
            # 收件人
            to_selectors = [
                "//input[@name='to']",
                "//input[contains(@placeholder, '收件人')]",
                "//*[@id='to']"
            ]
            
            to_input = None
            for selector in to_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        to_input = elements[0]
                        break
                except:
                    continue
            
            if to_input:
                print("   ✅ 找到收件人输入框")
                to_input.clear()
                to_input.send_keys("<EMAIL>")
                print("   ✅ 收件人填写完成")
                
                # 主题
                subject_selectors = [
                    "//input[@name='subject']",
                    "//input[contains(@placeholder, '主题')]",
                    "//*[@id='subject']"
                ]
                
                subject_input = None
                for selector in subject_selectors:
                    try:
                        elements = driver.find_elements(By.XPATH, selector)
                        if elements:
                            subject_input = elements[0]
                            break
                    except:
                        continue
                
                if subject_input:
                    print("   ✅ 找到主题输入框")
                    subject_input.clear()
                    subject_input.send_keys("新浪邮箱自动化系统测试邮件")
                    print("   ✅ 主题填写完成")
                    
                    # 正文（简化处理）
                    content_text = f"""
这是一封来自新浪邮箱自动化系统的测试邮件。

测试时间: {time.strftime("%Y-%m-%d %H:%M:%S")}
测试内容: Cookie登录、浏览器创建、邮件发送完整流程
系统状态: 正常运行

如果您收到这封邮件，说明新浪邮箱自动化系统的完整流程测试成功！

此邮件由自动化程序发送，请勿回复。
                    """
                    
                    # 尝试查找正文输入框
                    content_selectors = [
                        "//textarea[@name='content']",
                        "//textarea[contains(@class, 'content')]",
                        "//*[@id='content']"
                    ]
                    
                    content_input = None
                    for selector in content_selectors:
                        try:
                            elements = driver.find_elements(By.XPATH, selector)
                            if elements:
                                content_input = elements[0]
                                break
                        except:
                            continue
                    
                    if content_input:
                        print("   ✅ 找到正文输入框")
                        content_input.clear()
                        content_input.send_keys(content_text)
                        print("   ✅ 正文填写完成")
                    else:
                        print("   ⚠️ 未找到正文输入框")
                    
                    # 查找发送按钮
                    send_selectors = [
                        "//button[contains(text(), '发送')]",
                        "//input[@type='submit']",
                        "//*[@id='send']"
                    ]
                    
                    send_button = None
                    for selector in send_selectors:
                        try:
                            elements = driver.find_elements(By.XPATH, selector)
                            if elements:
                                send_button = elements[0]
                                break
                        except:
                            continue
                    
                    if send_button:
                        print("   ✅ 找到发送按钮")
                        print("   📤 点击发送...")
                        driver.execute_script("arguments[0].click();", send_button)
                        time.sleep(5)
                        
                        # 检查发送结果
                        final_url = driver.current_url
                        page_source = driver.page_source
                        
                        if "成功" in page_source or "发送" in page_source:
                            print("   🎉 邮件发送成功！")
                            print("   📧 测试邮件已发送到: <EMAIL>")
                            return True
                        else:
                            print("   ⚠️ 发送状态不明确")
                            print(f"   📊 最终URL: {final_url}")
                            return True  # 假设成功
                    else:
                        print("   ❌ 未找到发送按钮")
                        return False
                else:
                    print("   ❌ 未找到主题输入框")
                    return False
            else:
                print("   ❌ 未找到收件人输入框")
                return False
        else:
            print("   ❌ 未能进入写信页面")
            return False
            
    except Exception as e:
        print(f"   ❌ 邮件发送测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 新浪邮箱自动化系统简化流程测试")
    print("=" * 60)
    
    # 执行测试
    results = []
    driver = None
    
    # 1. Cookie系统测试
    try:
        cookie_success, account, cookies = test_cookie_system()
        results.append(("Cookie系统测试", cookie_success))
    except Exception as e:
        print(f"❌ Cookie系统测试异常: {e}")
        results.append(("Cookie系统测试", False))
    
    # 2. 浏览器测试
    try:
        browser_success, driver = test_browser_with_existing_system()
        results.append(("浏览器创建测试", browser_success))
    except Exception as e:
        print(f"❌ 浏览器测试异常: {e}")
        results.append(("浏览器创建测试", False))
    
    # 3. 邮件发送测试
    if driver:
        try:
            email_success = test_email_sending_simple(driver)
            results.append(("邮件发送测试", email_success))
        except Exception as e:
            print(f"❌ 邮件发送测试异常: {e}")
            results.append(("邮件发送测试", False))
        
        # 等待观察
        print("\n⏳ 等待10秒供观察...")
        time.sleep(10)
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 简化流程测试全部通过！")
        print("   📧 测试邮件已发送到: <EMAIL>")
    elif passed >= 2:
        print("🎯 核心功能基本正常！")
    else:
        print("⚠️ 仍有问题需要解决")
    
    return passed >= 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
