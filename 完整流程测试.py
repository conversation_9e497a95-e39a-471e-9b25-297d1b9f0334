#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整流程测试脚本
测试Cookie登录、浏览器创建、邮件发送的完整流程
并发送测试邮件到 <EMAIL>
"""

import sys
import os
import time
sys.path.insert(0, '.')

def test_cookie_files():
    """测试Cookie文件状态"""
    print("🔍 检查Cookie文件状态...")
    
    try:
        from pathlib import Path
        
        cookie_dir = Path("data/cookies")
        if not cookie_dir.exists():
            print("   ❌ Cookie目录不存在")
            return False
        
        cookie_files = list(cookie_dir.glob("*.cookies"))
        print(f"   📁 找到 {len(cookie_files)} 个Cookie文件:")
        
        for cookie_file in cookie_files:
            file_size = cookie_file.stat().st_size
            account_name = cookie_file.stem.replace('_sina_com', '@sina.com')
            print(f"      📄 {account_name}: {file_size} 字节")
        
        return len(cookie_files) > 0
        
    except Exception as e:
        print(f"   ❌ 检查Cookie文件失败: {e}")
        return False

def test_cookie_manager():
    """测试Cookie管理器"""
    print("\n🔧 测试Cookie管理器...")
    
    try:
        from src.core.cookie_manager import CookieManager
        from src.utils.config_manager import ConfigManager
        
        # 加载配置
        config_manager = ConfigManager()
        config_manager.load_config()
        config = getattr(config_manager, 'config', {})
        
        # 创建Cookie管理器
        cookie_manager = CookieManager(config)
        print("   ✅ Cookie管理器创建成功")
        
        # 测试加载Cookie
        test_accounts = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        
        for account_email in test_accounts:
            cookie_data = cookie_manager.get_cookies(account_email)
            if cookie_data and 'cookies' in cookie_data:
                cookies = cookie_data['cookies']
                print(f"   ✅ {account_email}: {len(cookies)} 个Cookie")
                
                # 显示部分Cookie信息
                for i, cookie in enumerate(cookies[:3]):
                    print(f"      Cookie {i+1}: {cookie.get('name', 'unknown')} = {cookie.get('value', '')[:20]}...")
                
                return True, account_email, cookies
            else:
                print(f"   ⚠️ {account_email}: 没有找到Cookie")
        
        print("   ❌ 所有测试账号都没有Cookie")
        return False, None, None
        
    except Exception as e:
        print(f"   ❌ Cookie管理器测试失败: {e}")
        return False, None, None

def test_browser_creation_with_cookie_login():
    """测试浏览器创建和Cookie登录"""
    print("\n🚀 测试浏览器创建和Cookie登录...")
    
    try:
        from src.core.browser_manager import BrowserManager
        from src.models.account import AccountManager
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        # 获取可用账号
        account_manager = AccountManager(db_manager)
        available_accounts = account_manager.get_available_accounts()
        
        if not available_accounts:
            print("   ❌ 没有可用账号")
            return False, None
        
        test_account = available_accounts[0]
        print(f"   🧪 测试账号: {test_account.email}")
        
        # 创建浏览器管理器
        config_dict = getattr(config, 'config', {})
        browser_config = config_dict.get('browser', {})
        browser_config['window_size'] = [1280, 720]
        browser_config['window_mode'] = '正常窗口'  # 使用正常窗口便于观察
        config_dict['browser'] = browser_config
        
        browser_manager = BrowserManager(config_dict)
        print("   ✅ 浏览器管理器创建成功")
        
        # 创建浏览器驱动（包含Cookie登录）
        print(f"   🚀 创建浏览器驱动并测试Cookie登录...")
        driver_id = browser_manager.create_driver(test_account)
        
        if driver_id:
            print(f"   ✅ 浏览器驱动创建成功: {driver_id}")
            
            # 获取驱动并检查状态
            driver = browser_manager.get_driver(driver_id)
            if driver:
                print("   ✅ 浏览器驱动获取成功")
                
                # 检查当前URL和标题
                current_url = driver.current_url
                page_title = driver.title
                print(f"   📊 当前URL: {current_url}")
                print(f"   📊 页面标题: {page_title}")
                
                # 判断登录状态
                login_success = False
                if "mail.sina.com.cn" in current_url:
                    if "login" not in current_url.lower():
                        print("   ✅ 登录状态：已成功登录到新浪邮箱")
                        login_success = True
                    else:
                        print("   ⚠️ 登录状态：仍在登录页面")
                else:
                    print("   ❌ 登录状态：未导航到新浪邮箱")
                
                return login_success, driver
            else:
                print("   ❌ 浏览器驱动获取失败")
                return False, None
        else:
            print("   ❌ 浏览器驱动创建失败")
            return False, None
            
    except Exception as e:
        print(f"   ❌ 浏览器创建测试失败: {e}")
        return False, None

def test_email_sending(driver):
    """测试邮件发送功能"""
    print("\n📧 测试邮件发送功能...")
    
    try:
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        # 查找写信按钮
        print("   🔍 查找写信按钮...")
        try:
            # 等待页面加载完成
            time.sleep(3)
            
            # 尝试多种写信按钮定位方式
            write_button_selectors = [
                "//a[contains(text(), '写信')]",
                "//a[contains(@href, 'compose')]",
                "//*[@id='compose']",
                "//a[contains(@class, 'compose')]",
                "//button[contains(text(), '写信')]"
            ]
            
            write_button = None
            for selector in write_button_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        write_button = elements[0]
                        print(f"   ✅ 找到写信按钮: {selector}")
                        break
                except:
                    continue
            
            if not write_button:
                print("   ❌ 未找到写信按钮")
                return False
            
            # 点击写信按钮
            print("   🖱️ 点击写信按钮...")
            write_button.click()
            time.sleep(3)
            
            # 查找邮件编写表单
            print("   📝 查找邮件编写表单...")
            
            # 收件人输入框
            to_input = None
            to_selectors = [
                "//input[@name='to']",
                "//input[contains(@placeholder, '收件人')]",
                "//*[@id='to']",
                "//input[contains(@class, 'to')]"
            ]
            
            for selector in to_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        to_input = elements[0]
                        print(f"   ✅ 找到收件人输入框: {selector}")
                        break
                except:
                    continue
            
            if not to_input:
                print("   ❌ 未找到收件人输入框")
                return False
            
            # 主题输入框
            subject_input = None
            subject_selectors = [
                "//input[@name='subject']",
                "//input[contains(@placeholder, '主题')]",
                "//*[@id='subject']",
                "//input[contains(@class, 'subject')]"
            ]
            
            for selector in subject_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        subject_input = elements[0]
                        print(f"   ✅ 找到主题输入框: {selector}")
                        break
                except:
                    continue
            
            if not subject_input:
                print("   ❌ 未找到主题输入框")
                return False
            
            # 填写邮件内容
            print("   ✍️ 填写邮件内容...")
            
            # 收件人
            to_input.clear()
            to_input.send_keys("<EMAIL>")
            print("   ✅ 收件人填写完成: <EMAIL>")
            
            # 主题
            subject_input.clear()
            subject_input.send_keys("新浪邮箱自动化系统测试邮件")
            print("   ✅ 主题填写完成")
            
            # 邮件正文
            content_input = None
            content_selectors = [
                "//textarea[@name='content']",
                "//div[contains(@class, 'editor')]//iframe",
                "//*[@id='content']",
                "//textarea[contains(@class, 'content')]"
            ]
            
            for selector in content_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        content_input = elements[0]
                        print(f"   ✅ 找到正文输入框: {selector}")
                        break
                except:
                    continue
            
            if content_input:
                if content_input.tag_name == 'iframe':
                    # 如果是iframe，需要切换到iframe内部
                    driver.switch_to.frame(content_input)
                    body = driver.find_element(By.TAG_NAME, "body")
                    body.clear()
                    body.send_keys("""
这是一封来自新浪邮箱自动化系统的测试邮件。

测试时间: """ + time.strftime("%Y-%m-%d %H:%M:%S") + """
测试内容: Cookie登录、浏览器创建、邮件发送完整流程
系统状态: 正常运行

如果您收到这封邮件，说明新浪邮箱自动化系统的完整流程测试成功！

此邮件由自动化程序发送，请勿回复。
                    """)
                    driver.switch_to.default_content()
                else:
                    content_input.clear()
                    content_input.send_keys("""
这是一封来自新浪邮箱自动化系统的测试邮件。

测试时间: """ + time.strftime("%Y-%m-%d %H:%M:%S") + """
测试内容: Cookie登录、浏览器创建、邮件发送完整流程
系统状态: 正常运行

如果您收到这封邮件，说明新浪邮箱自动化系统的完整流程测试成功！

此邮件由自动化程序发送，请勿回复。
                    """)
                
                print("   ✅ 正文填写完成")
            else:
                print("   ⚠️ 未找到正文输入框，跳过正文填写")
            
            # 查找发送按钮
            print("   🚀 查找发送按钮...")
            send_button = None
            send_selectors = [
                "//button[contains(text(), '发送')]",
                "//input[@type='submit' and contains(@value, '发送')]",
                "//*[@id='send']",
                "//a[contains(text(), '发送')]"
            ]
            
            for selector in send_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        send_button = elements[0]
                        print(f"   ✅ 找到发送按钮: {selector}")
                        break
                except:
                    continue
            
            if not send_button:
                print("   ❌ 未找到发送按钮")
                return False
            
            # 点击发送按钮
            print("   📤 点击发送按钮...")
            send_button.click()
            time.sleep(5)
            
            # 检查发送结果
            print("   🔍 检查发送结果...")
            
            # 查找成功提示
            success_indicators = [
                "发送成功",
                "邮件已发送",
                "发送完成",
                "success"
            ]
            
            page_source = driver.page_source.lower()
            send_success = False
            
            for indicator in success_indicators:
                if indicator.lower() in page_source:
                    send_success = True
                    print(f"   ✅ 发送成功指示: {indicator}")
                    break
            
            if send_success:
                print("   🎉 邮件发送成功！")
                print("   📧 测试邮件已发送到: <EMAIL>")
                return True
            else:
                print("   ⚠️ 未找到明确的发送成功提示")
                print("   📊 当前页面URL:", driver.current_url)
                return False
            
        except Exception as e:
            print(f"   ❌ 邮件发送过程失败: {e}")
            return False
            
    except Exception as e:
        print(f"   ❌ 邮件发送测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 新浪邮箱自动化系统完整流程测试")
    print("=" * 60)
    
    # 测试步骤
    tests = [
        ("Cookie文件检查", test_cookie_files),
        ("Cookie管理器测试", test_cookie_manager),
        ("浏览器创建和Cookie登录", test_browser_creation_with_cookie_login),
    ]
    
    results = []
    driver = None
    
    for test_name, test_func in tests:
        try:
            if test_name == "浏览器创建和Cookie登录":
                result, driver = test_func()
                results.append((test_name, result))
            else:
                result = test_func()
                results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 如果浏览器创建成功，测试邮件发送
    if driver:
        try:
            email_result = test_email_sending(driver)
            results.append(("邮件发送测试", email_result))
        except Exception as e:
            print(f"❌ 邮件发送测试异常: {e}")
            results.append(("邮件发送测试", False))
        
        # 等待用户观察
        print("\n⏳ 等待10秒供观察...")
        time.sleep(10)
        
        # 清理
        try:
            driver.quit()
            print("✅ 浏览器清理成功")
        except:
            pass
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 完整流程测试全部通过！")
        print("\n💡 系统功能:")
        print("   ✅ Cookie文件管理正常")
        print("   ✅ Cookie管理器工作正常")
        print("   ✅ 浏览器自动创建和Cookie登录成功")
        print("   ✅ 邮件发送功能正常")
        print("   📧 测试邮件已发送到: <EMAIL>")
    elif passed >= 3:
        print("🎯 核心功能正常！")
        print("   Cookie登录和浏览器创建功能完全正常")
    else:
        print("⚠️ 仍有重要问题需要解决")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
