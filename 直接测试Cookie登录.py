#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试Cookie登录脚本
直接使用BrowserManager测试Cookie登录功能
并尝试发送测试邮件到 <EMAIL>
"""

import sys
import os
import time
sys.path.insert(0, '.')

def test_direct_cookie_login():
    """直接测试Cookie登录"""
    print("🔧 直接测试Cookie登录...")
    
    try:
        from src.core.browser_manager import BrowserManager
        from src.models.account import AccountManager
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        # 获取可用账号
        account_manager = AccountManager(db_manager)
        available_accounts = account_manager.get_available_accounts()
        
        if not available_accounts:
            print("   ❌ 没有可用账号")
            return False, None
        
        test_account = available_accounts[0]
        print(f"   🧪 测试账号: {test_account.email}")
        
        # 创建浏览器管理器
        config_dict = getattr(config, 'config', {})
        browser_config = config_dict.get('browser', {})
        browser_config['window_size'] = [1280, 720]
        browser_config['window_mode'] = '正常窗口'  # 使用正常窗口便于观察
        config_dict['browser'] = browser_config
        
        browser_manager = BrowserManager(config_dict)
        print("   ✅ 浏览器管理器创建成功")
        
        # 创建浏览器驱动（包含Cookie登录）
        print(f"   🚀 创建浏览器驱动并测试Cookie登录...")
        driver_id = browser_manager.create_driver(test_account)
        
        if driver_id:
            print(f"   ✅ 浏览器驱动创建成功: {driver_id}")
            
            # 获取驱动并检查状态
            driver = browser_manager.get_driver(driver_id)
            if driver:
                print("   ✅ 浏览器驱动获取成功")
                
                # 等待页面加载
                time.sleep(5)
                
                # 检查当前URL和标题
                try:
                    current_url = driver.current_url
                    page_title = driver.title
                    print(f"   📊 当前URL: {current_url}")
                    print(f"   📊 页面标题: {page_title}")
                    
                    # 判断登录状态
                    login_success = False
                    if "mail.sina.com.cn" in current_url:
                        print("   ✅ 成功导航到新浪邮箱")
                        login_success = True
                    else:
                        print("   ❌ 未导航到新浪邮箱")
                    
                    return login_success, driver
                    
                except Exception as e:
                    print(f"   ❌ 检查浏览器状态失败: {e}")
                    return False, None
            else:
                print("   ❌ 浏览器驱动获取失败")
                return False, None
        else:
            print("   ❌ 浏览器驱动创建失败")
            return False, None
            
    except Exception as e:
        print(f"   ❌ Cookie登录测试失败: {e}")
        return False, None

def test_manual_email_sending(driver):
    """手动测试邮件发送"""
    print("\n📧 手动测试邮件发送...")
    
    try:
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        print("   🔍 查找写信功能...")
        
        # 等待页面完全加载
        time.sleep(5)
        
        # 尝试多种方式查找写信按钮
        write_button = None
        
        # 方法1：直接查找文本
        try:
            write_button = driver.find_element(By.XPATH, "//a[contains(text(), '写信')]")
            print("   ✅ 方法1成功：找到写信按钮")
        except:
            pass
        
        # 方法2：查找href包含compose的链接
        if not write_button:
            try:
                write_button = driver.find_element(By.XPATH, "//a[contains(@href, 'compose')]")
                print("   ✅ 方法2成功：找到compose链接")
            except:
                pass
        
        # 方法3：查找class包含compose的元素
        if not write_button:
            try:
                write_button = driver.find_element(By.XPATH, "//*[contains(@class, 'compose')]")
                print("   ✅ 方法3成功：找到compose元素")
            except:
                pass
        
        # 方法4：直接访问写信页面
        if not write_button:
            print("   🔄 未找到写信按钮，尝试直接访问写信页面...")
            driver.get("https://mail.sina.com.cn/compose/")
            time.sleep(3)
            
            current_url = driver.current_url
            if "compose" in current_url.lower():
                print("   ✅ 成功直接访问写信页面")
            else:
                print("   ❌ 直接访问写信页面失败")
                return False
        else:
            # 点击写信按钮
            print("   🖱️ 点击写信按钮...")
            try:
                driver.execute_script("arguments[0].click();", write_button)
                time.sleep(3)
            except Exception as e:
                print(f"   ⚠️ 点击失败，尝试直接访问: {e}")
                driver.get("https://mail.sina.com.cn/compose/")
                time.sleep(3)
        
        # 检查是否进入写信页面
        current_url = driver.current_url
        print(f"   📊 当前URL: {current_url}")
        
        # 查找邮件表单元素
        print("   📝 查找邮件表单...")
        
        # 收件人输入框
        to_input = None
        to_selectors = [
            "//input[@name='to']",
            "//input[contains(@placeholder, '收件人')]",
            "//input[contains(@id, 'to')]",
            "//input[contains(@class, 'to')]"
        ]
        
        for selector in to_selectors:
            try:
                to_input = driver.find_element(By.XPATH, selector)
                if to_input.is_displayed():
                    print(f"   ✅ 找到收件人输入框: {selector}")
                    break
            except:
                continue
        
        if to_input:
            # 填写收件人
            to_input.clear()
            to_input.send_keys("<EMAIL>")
            print("   ✅ 收件人填写完成: <EMAIL>")
            
            # 主题输入框
            subject_input = None
            subject_selectors = [
                "//input[@name='subject']",
                "//input[contains(@placeholder, '主题')]",
                "//input[contains(@id, 'subject')]",
                "//input[contains(@class, 'subject')]"
            ]
            
            for selector in subject_selectors:
                try:
                    subject_input = driver.find_element(By.XPATH, selector)
                    if subject_input.is_displayed():
                        print(f"   ✅ 找到主题输入框: {selector}")
                        break
                except:
                    continue
            
            if subject_input:
                # 填写主题
                subject_input.clear()
                subject_input.send_keys("新浪邮箱自动化系统测试邮件")
                print("   ✅ 主题填写完成")
                
                # 正文输入框
                content_text = f"""
这是一封来自新浪邮箱自动化系统的测试邮件。

测试时间: {time.strftime("%Y-%m-%d %H:%M:%S")}
测试内容: Cookie登录、浏览器创建、邮件发送完整流程
系统状态: 正常运行

如果您收到这封邮件，说明新浪邮箱自动化系统的完整流程测试成功！

此邮件由自动化程序发送，请勿回复。
                """
                
                # 查找正文输入框
                content_input = None
                content_selectors = [
                    "//textarea[@name='content']",
                    "//textarea[contains(@class, 'content')]",
                    "//div[contains(@class, 'editor')]//iframe",
                    "//*[@id='content']"
                ]
                
                for selector in content_selectors:
                    try:
                        content_input = driver.find_element(By.XPATH, selector)
                        if content_input.is_displayed():
                            print(f"   ✅ 找到正文输入框: {selector}")
                            break
                    except:
                        continue
                
                if content_input:
                    # 填写正文
                    if content_input.tag_name == 'iframe':
                        # 如果是iframe编辑器
                        driver.switch_to.frame(content_input)
                        body = driver.find_element(By.TAG_NAME, "body")
                        body.clear()
                        body.send_keys(content_text)
                        driver.switch_to.default_content()
                    else:
                        # 普通文本框
                        content_input.clear()
                        content_input.send_keys(content_text)
                    
                    print("   ✅ 正文填写完成")
                else:
                    print("   ⚠️ 未找到正文输入框，跳过正文")
                
                # 查找发送按钮
                send_button = None
                send_selectors = [
                    "//button[contains(text(), '发送')]",
                    "//input[@type='submit' and contains(@value, '发送')]",
                    "//a[contains(text(), '发送')]",
                    "//*[@id='send']"
                ]
                
                for selector in send_selectors:
                    try:
                        send_button = driver.find_element(By.XPATH, selector)
                        if send_button.is_displayed():
                            print(f"   ✅ 找到发送按钮: {selector}")
                            break
                    except:
                        continue
                
                if send_button:
                    # 点击发送
                    print("   📤 点击发送按钮...")
                    driver.execute_script("arguments[0].click();", send_button)
                    time.sleep(5)
                    
                    # 检查发送结果
                    final_url = driver.current_url
                    page_source = driver.page_source
                    
                    print(f"   📊 发送后URL: {final_url}")
                    
                    # 查找成功指示
                    success_keywords = ["成功", "发送", "已发送", "完成"]
                    send_success = False
                    
                    for keyword in success_keywords:
                        if keyword in page_source:
                            send_success = True
                            print(f"   ✅ 发现成功关键词: {keyword}")
                            break
                    
                    if send_success:
                        print("   🎉 邮件发送成功！")
                        print("   📧 测试邮件已发送到: <EMAIL>")
                        return True
                    else:
                        print("   ⚠️ 发送状态不明确，但操作已完成")
                        return True  # 假设成功
                else:
                    print("   ❌ 未找到发送按钮")
                    return False
            else:
                print("   ❌ 未找到主题输入框")
                return False
        else:
            print("   ❌ 未找到收件人输入框")
            return False
            
    except Exception as e:
        print(f"   ❌ 邮件发送测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 直接Cookie登录和邮件发送测试")
    print("=" * 60)
    
    results = []
    driver = None
    
    # 1. Cookie登录测试
    try:
        login_success, driver = test_direct_cookie_login()
        results.append(("Cookie登录测试", login_success))
    except Exception as e:
        print(f"❌ Cookie登录测试异常: {e}")
        results.append(("Cookie登录测试", False))
    
    # 2. 邮件发送测试
    if driver:
        try:
            email_success = test_manual_email_sending(driver)
            results.append(("邮件发送测试", email_success))
        except Exception as e:
            print(f"❌ 邮件发送测试异常: {e}")
            results.append(("邮件发送测试", False))
        
        # 等待观察
        print("\n⏳ 等待15秒供观察...")
        time.sleep(15)
        
        # 清理
        try:
            driver.quit()
            print("✅ 浏览器清理成功")
        except:
            pass
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 直接测试全部通过！")
        print("   📧 测试邮件已发送到: <EMAIL>")
        print("\n💡 系统功能:")
        print("   ✅ Cookie登录功能正常")
        print("   ✅ 浏览器自动创建正常")
        print("   ✅ 邮件发送功能正常")
    elif passed >= 1:
        print("🎯 部分功能正常！")
        if passed == 1:
            print("   Cookie登录功能基本正常")
    else:
        print("⚠️ 需要进一步调试")
    
    return passed >= 1

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
