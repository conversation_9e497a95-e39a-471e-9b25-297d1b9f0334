#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多浏览器并发发送系统集成测试
验证整个系统的功能完整性和性能表现
"""

import unittest
import time
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from typing import List

# 添加项目根目录到路径
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.models.account import Account
from src.core.smart_account_strategy import SmartAccountStrategy, SendingMode, BrowserMode
from src.core.intelligent_task_distributor import IntelligentTaskDistributor
from src.core.enhanced_cookie_account_switcher import Enhanced<PERSON>ookieAccountSwitcher
from src.core.concurrent_email_sender_manager import ConcurrentEmailSenderManager, ConcurrentSendingConfig
from src.core.multi_browser_integration_adapter import MultiBrowserIntegrationAdapter, IntegrationConfig


class TestConcurrentSendingIntegration(unittest.TestCase):
    """并发发送系统集成测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试账号
        self.test_accounts = [
            Account(
                email="<EMAIL>",
                password="password1",
                smtp_server="smtp.sina.com",
                smtp_port=587,
                imap_server="imap.sina.com",
                imap_port=993
            ),
            Account(
                email="<EMAIL>", 
                password="password2",
                smtp_server="smtp.sina.com",
                smtp_port=587,
                imap_server="imap.sina.com",
                imap_port=993
            ),
            Account(
                email="<EMAIL>",
                password="password3", 
                smtp_server="smtp.sina.com",
                smtp_port=587,
                imap_server="imap.sina.com",
                imap_port=993
            )
        ]
        
        # 创建测试收件人
        self.test_recipients = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        # 清理临时目录
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_smart_account_strategy_analysis(self):
        """测试智能账号策略分析"""
        print("\n🧪 测试智能账号策略分析...")
        
        strategy_analyzer = SmartAccountStrategy()
        
        # 测试单浏览器模式
        strategy = strategy_analyzer.analyze_sending_strategy(
            available_accounts=self.test_accounts[:1],
            recipient_emails=self.test_recipients[:3],
            sending_mode=SendingMode.INDIVIDUAL
        )
        
        self.assertEqual(strategy.browser_mode, BrowserMode.SINGLE)
        self.assertEqual(strategy.browser_count, 1)
        self.assertEqual(len(strategy.accounts_per_browser), 1)
        
        print(f"✅ 单浏览器模式测试通过：{strategy.browser_count}个浏览器")
        
        # 测试多浏览器模式
        strategy = strategy_analyzer.analyze_sending_strategy(
            available_accounts=self.test_accounts,
            recipient_emails=self.test_recipients,
            sending_mode=SendingMode.INDIVIDUAL,
            user_browser_count=3
        )
        
        self.assertEqual(strategy.browser_mode, BrowserMode.MULTI)
        self.assertEqual(strategy.browser_count, 3)
        self.assertEqual(len(strategy.accounts_per_browser), 3)
        
        print(f"✅ 多浏览器模式测试通过：{strategy.browser_count}个浏览器")
    
    def test_intelligent_task_distribution(self):
        """测试智能任务分配"""
        print("\n🧪 测试智能任务分配...")
        
        # 创建策略
        strategy_analyzer = SmartAccountStrategy()
        strategy = strategy_analyzer.analyze_sending_strategy(
            available_accounts=self.test_accounts,
            recipient_emails=self.test_recipients,
            sending_mode=SendingMode.INDIVIDUAL,
            user_browser_count=2
        )
        
        # 创建任务分配器
        distributor = IntelligentTaskDistributor()
        browser_queues = distributor.distribute_tasks(
            strategy=strategy,
            recipient_emails=self.test_recipients,
            subject="测试邮件",
            content="这是一封测试邮件",
            sending_mode=SendingMode.INDIVIDUAL
        )
        
        # 验证分配结果
        self.assertEqual(len(browser_queues), 2)
        
        total_tasks = sum(len(queue.task_queue) for queue in browser_queues.values())
        self.assertEqual(total_tasks, len(self.test_recipients))
        
        print(f"✅ 任务分配测试通过：{total_tasks}个任务分配到{len(browser_queues)}个浏览器")
    
    def test_integration_adapter_analysis(self):
        """测试集成适配器分析功能"""
        print("\n🧪 测试集成适配器分析功能...")
        
        config = IntegrationConfig(
            enable_concurrent_mode=True,
            max_browsers=5,
            send_interval=1.0
        )
        
        adapter = MultiBrowserIntegrationAdapter(config)
        
        # 测试发送需求分析
        analysis = adapter.analyze_sending_requirements(
            accounts=self.test_accounts,
            recipient_emails=self.test_recipients,
            user_browser_count=3,
            sending_mode="individual"
        )
        
        self.assertIn('strategy', analysis)
        self.assertIn('allocation', analysis)
        self.assertIn('recommendations', analysis)
        
        strategy = analysis['strategy']
        self.assertEqual(strategy['browser_count'], 3)
        self.assertEqual(strategy['total_emails'], len(self.test_recipients))
        
        print(f"✅ 需求分析测试通过：推荐{strategy['browser_count']}个浏览器")
    
    @patch('src.core.concurrent_email_sender_manager.webdriver.Chrome')
    def test_concurrent_manager_initialization(self, mock_chrome):
        """测试并发管理器初始化（模拟浏览器）"""
        print("\n🧪 测试并发管理器初始化...")
        
        # 模拟浏览器驱动
        mock_driver = MagicMock()
        mock_chrome.return_value = mock_driver
        
        config = ConcurrentSendingConfig(
            max_browsers=2,
            send_interval=0.1,
            account_switch_threshold=2,
            enable_headless=True
        )
        
        manager = ConcurrentEmailSenderManager(config)
        
        # 验证初始化
        self.assertIsNotNone(manager.strategy_analyzer)
        self.assertIsNotNone(manager.task_distributor)
        self.assertEqual(manager.config.max_browsers, 2)
        
        print("✅ 并发管理器初始化测试通过")
    
    def test_performance_estimation(self):
        """测试性能预估"""
        print("\n🧪 测试性能预估...")
        
        strategy_analyzer = SmartAccountStrategy()
        
        # 测试大量邮件的性能预估
        large_recipients = [f"user{i}@example.com" for i in range(100)]
        
        strategy = strategy_analyzer.analyze_sending_strategy(
            available_accounts=self.test_accounts,
            recipient_emails=large_recipients,
            sending_mode=SendingMode.INDIVIDUAL,
            user_browser_count=5
        )
        
        # 验证预估时间合理
        self.assertGreater(strategy.estimated_time, 0)
        self.assertLess(strategy.estimated_time, 1000)  # 不应该超过1000分钟
        
        print(f"✅ 性能预估测试通过：{len(large_recipients)}封邮件预计{strategy.estimated_time:.1f}分钟")
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n🧪 测试错误处理...")
        
        # 测试空账号列表
        strategy_analyzer = SmartAccountStrategy()
        strategy = strategy_analyzer.analyze_sending_strategy(
            available_accounts=[],
            recipient_emails=self.test_recipients,
            sending_mode=SendingMode.INDIVIDUAL
        )
        
        # 应该返回默认策略
        self.assertIsNotNone(strategy)
        
        # 测试空收件人列表
        strategy = strategy_analyzer.analyze_sending_strategy(
            available_accounts=self.test_accounts,
            recipient_emails=[],
            sending_mode=SendingMode.INDIVIDUAL
        )
        
        self.assertIsNotNone(strategy)
        self.assertEqual(strategy.total_emails, 0)
        
        print("✅ 错误处理测试通过")
    
    def test_configuration_validation(self):
        """测试配置验证"""
        print("\n🧪 测试配置验证...")
        
        # 测试各种配置组合
        configs = [
            IntegrationConfig(max_browsers=1, send_interval=0.5),
            IntegrationConfig(max_browsers=10, send_interval=5.0),
            IntegrationConfig(enable_concurrent_mode=False),
            IntegrationConfig(enable_headless=True)
        ]
        
        for config in configs:
            adapter = MultiBrowserIntegrationAdapter(config)
            self.assertIsNotNone(adapter)
            self.assertEqual(adapter.config.max_browsers, config.max_browsers)
        
        print("✅ 配置验证测试通过")
    
    def test_memory_usage(self):
        """测试内存使用"""
        print("\n🧪 测试内存使用...")
        
        import psutil
        import gc
        
        # 记录初始内存
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建大量对象
        strategy_analyzer = SmartAccountStrategy()
        distributors = []
        
        for i in range(10):
            distributor = IntelligentTaskDistributor()
            distributors.append(distributor)
        
        # 执行垃圾回收
        gc.collect()
        
        # 检查内存增长
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # 内存增长应该在合理范围内（小于100MB）
        self.assertLess(memory_increase, 100)
        
        print(f"✅ 内存使用测试通过：增长{memory_increase:.1f}MB")


def run_integration_tests():
    """运行集成测试"""
    print("🚀 开始多浏览器并发发送系统集成测试...")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestConcurrentSendingIntegration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 60)
    if result.wasSuccessful():
        print("🎉 所有集成测试通过！系统准备就绪。")
    else:
        print("❌ 部分测试失败，请检查问题。")
        for failure in result.failures:
            print(f"失败: {failure[0]}")
            print(f"原因: {failure[1]}")
        for error in result.errors:
            print(f"错误: {error[0]}")
            print(f"原因: {error[1]}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
