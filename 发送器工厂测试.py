#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发送器工厂测试脚本
测试EmailSendingManager的发送器工厂设置
"""

import sys
import os
sys.path.insert(0, '.')

def test_email_sending_manager_factory():
    """测试邮件发送管理器的发送器工厂"""
    print("🔧 测试邮件发送管理器的发送器工厂...")
    
    try:
        from src.core.email_sending_manager import EmailSendingManager, SendingConfig
        
        # 创建发送配置
        config = SendingConfig()
        
        # 创建邮件发送管理器
        manager = EmailSendingManager(config)
        
        print("📋 发送器工厂状态检查:")
        
        # 检查初始状态
        has_attr = hasattr(manager, 'sender_factory')
        factory_value = getattr(manager, 'sender_factory', None)
        
        print(f"   📊 has_sender_factory: {has_attr}")
        print(f"   📊 sender_factory_value: {factory_value}")
        print(f"   📊 sender_factory_type: {type(factory_value)}")
        
        # 测试设置发送器工厂
        def test_factory():
            return "test_sender"
        
        print("\n🔧 设置测试发送器工厂...")
        manager.set_sender_factory(test_factory)
        
        # 检查设置后的状态
        has_attr_after = hasattr(manager, 'sender_factory')
        factory_value_after = getattr(manager, 'sender_factory', None)
        
        print(f"   📊 设置后 has_sender_factory: {has_attr_after}")
        print(f"   📊 设置后 sender_factory_value: {factory_value_after}")
        print(f"   📊 设置后 sender_factory_type: {type(factory_value_after)}")
        
        # 测试调用工厂
        if factory_value_after:
            try:
                result = factory_value_after()
                print(f"   ✅ 工厂调用成功: {result}")
            except Exception as e:
                print(f"   ❌ 工厂调用失败: {e}")
        
        # 测试start_sending方法
        print("\n🚀 测试start_sending方法...")
        try:
            # 这应该会失败，因为没有实际的发送器，但不应该因为"未设置发送器工厂"而失败
            result = manager.start_sending()
            print(f"   📊 start_sending结果: {result}")
        except Exception as e:
            error_msg = str(e)
            if "未设置发送器工厂" in error_msg:
                print(f"   ❌ 仍然报告未设置发送器工厂: {error_msg}")
                return False
            else:
                print(f"   ✅ 不再报告发送器工厂问题，其他错误: {error_msg}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_task_sending_manager_integration():
    """测试任务发送管理器集成"""
    print("\n🔧 测试任务发送管理器集成...")
    
    try:
        from src.core.email_sending_manager import EmailSendingManager, SendingConfig
        from src.core.smart_task_queue import SmartTaskQueue, QueueConfig
        from src.core.batch_processor import BatchProcessor, BatchConfig
        from src.utils.config_manager import ConfigManager
        
        # 初始化配置
        config_manager = ConfigManager()
        config_manager.load_config()
        db_path = config_manager.get('database.path', 'data/sina_email.db')
        
        # 创建组件
        queue_config = QueueConfig()
        batch_config = BatchConfig()
        sending_config = SendingConfig()
        
        task_queue = SmartTaskQueue(queue_config, db_path)
        batch_processor = BatchProcessor(task_queue, batch_config)
        manager = EmailSendingManager(sending_config)
        
        print("📋 集成测试:")
        
        # 检查管理器状态
        status = manager.get_sending_status()
        print(f"   📊 任务状态: {status}")
        
        # 检查发送器工厂
        has_factory = hasattr(manager, 'sender_factory') and manager.sender_factory is not None
        print(f"   📊 有发送器工厂: {has_factory}")
        
        # 设置测试工厂
        def test_sender_factory():
            return "test_unified_sender"
        
        manager.set_sender_factory(test_sender_factory)
        print("   ✅ 测试工厂设置完成")
        
        # 再次检查
        has_factory_after = hasattr(manager, 'sender_factory') and manager.sender_factory is not None
        print(f"   📊 设置后有发送器工厂: {has_factory_after}")
        
        return has_factory_after
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def test_unified_email_sender():
    """测试统一邮件发送器"""
    print("\n🔧 测试统一邮件发送器...")
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender
        from src.models.account import Account
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        # 创建配置
        config = ConfigManager()
        config.load_config()
        db_path = config.get('database.path', 'data/sina_email.db')
        db_manager = DatabaseManager(db_path)
        
        # 创建测试账号
        test_accounts = [
            Account(
                id=1,
                email="<EMAIL>",
                password="test123",
                status="active"
            )
        ]
        
        print("📋 统一邮件发送器测试:")
        
        # 测试创建发送器（不需要实际的浏览器驱动）
        try:
            # 这里我们不传递driver，看看会发生什么
            sender = UnifiedEmailSender(
                driver=None,  # 故意传递None
                accounts=test_accounts,
                config=config,
                db_manager=db_manager
            )
            print("   ✅ 统一邮件发送器创建成功（即使没有driver）")
            return True
        except Exception as e:
            print(f"   📊 统一邮件发送器创建失败: {e}")
            # 这是预期的，因为没有driver
            return True
        
    except Exception as e:
        print(f"❌ 统一邮件发送器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 发送器工厂问题诊断测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("邮件发送管理器工厂", test_email_sending_manager_factory),
        ("任务发送管理器集成", test_task_sending_manager_integration),
        ("统一邮件发送器", test_unified_email_sender)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 发送器工厂功能正常！")
        print("\n💡 问题可能在于:")
        print("   1. 浏览器驱动创建失败")
        print("   2. 账号管理器未正确初始化")
        print("   3. 发送器工厂设置时机不对")
    else:
        print("⚠️ 发送器工厂存在问题")
        print("\n💡 建议检查:")
        print("   1. EmailSendingManager的set_sender_factory方法")
        print("   2. 发送器工厂的调用逻辑")
        print("   3. 统一邮件发送器的创建过程")
    
    return passed >= 2  # 至少2个测试通过

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
