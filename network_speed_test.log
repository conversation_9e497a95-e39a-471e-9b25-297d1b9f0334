2025-08-07 00:58:31,162 | INFO | __main__ | 🌐 测试纯连接速度...
2025-08-07 00:58:31,162 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 00:58:31,163 | INFO | __main__ |    ✅ 配置了 40 个超快速优化参数
2025-08-07 00:58:31,163 | INFO | WDM | ====== WebDriver manager ======
2025-08-07 00:58:36,140 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 00:58:36,355 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 00:58:36,565 | INFO | WDM | Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver.exe] found in cache
2025-08-07 00:58:43,252 | ERROR | __main__ |    ❌ 连接测试失败: Message: timeout: Timed out receiving message from renderer: -0.006
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x53ba83+63395]
	GetHandleVerifier [0x0x53bac4+63460]
	(No symbol) [0x0x382113]
	(No symbol) [0x0x372e6b]
	(No symbol) [0x0x372ba1]
	(No symbol) [0x0x370b44]
	(No symbol) [0x0x37160d]
	(No symbol) [0x0x37dda9]
	(No symbol) [0x0x38f2b5]
	(No symbol) [0x0x394cb6]
	(No symbol) [0x0x371c4d]
	(No symbol) [0x0x38f019]
	(No symbol) [0x0x4106c9]
	(No symbol) [0x0x3ef1a6]
	(No symbol) [0x0x3be7b2]
	(No symbol) [0x0x3bf654]
	GetHandleVerifier [0x0x7b8883+2672035]
	GetHandleVerifier [0x0x7b3cba+2652634]
	GetHandleVerifier [0x0x562bca+223466]
	GetHandleVerifier [0x0x552cb8+158168]
	GetHandleVerifier [0x0x55978d+185517]
	GetHandleVerifier [0x0x543b78+96408]
	GetHandleVerifier [0x0x543d02+96802]
	GetHandleVerifier [0x0x52e90a+9770]
	BaseThreadInitThunk [0x0x76cafa29+25]
	RtlGetAppContainerNamedObjectPath [0x0x77dd7a9e+286]
	RtlGetAppContainerNamedObjectPath [0x0x77dd7a6e+238]
 (12.089秒)
2025-08-07 00:58:45,446 | INFO | __main__ | 🌐 测试纯连接速度...
2025-08-07 00:58:45,447 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 00:58:45,448 | INFO | __main__ |    ✅ 配置了 40 个超快速优化参数
2025-08-07 00:58:45,448 | INFO | WDM | ====== WebDriver manager ======
2025-08-07 00:58:50,655 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 00:58:50,868 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 00:58:51,073 | INFO | WDM | Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver.exe] found in cache
2025-08-07 00:58:53,381 | INFO | __main__ |    ⚡ 纯连接测试: 成功 (7.923秒)
2025-08-07 00:58:55,585 | INFO | __main__ | 🌐 测试纯连接速度...
2025-08-07 00:58:55,586 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 00:58:55,586 | INFO | __main__ |    ✅ 配置了 40 个超快速优化参数
2025-08-07 00:58:55,587 | INFO | WDM | ====== WebDriver manager ======
2025-08-07 00:59:00,910 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 00:59:01,116 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 00:59:01,348 | INFO | WDM | Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver.exe] found in cache
2025-08-07 00:59:07,994 | ERROR | __main__ |    ❌ 连接测试失败: Message: timeout: Timed out receiving message from renderer: 4.836
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x53ba83+63395]
	GetHandleVerifier [0x0x53bac4+63460]
	(No symbol) [0x0x382113]
	(No symbol) [0x0x372e6b]
	(No symbol) [0x0x372ba1]
	(No symbol) [0x0x370b44]
	(No symbol) [0x0x37160d]
	(No symbol) [0x0x37dda9]
	(No symbol) [0x0x38f2b5]
	(No symbol) [0x0x394cb6]
	(No symbol) [0x0x371c4d]
	(No symbol) [0x0x38f019]
	(No symbol) [0x0x410a9c]
	(No symbol) [0x0x3ef1a6]
	(No symbol) [0x0x3be7b2]
	(No symbol) [0x0x3bf654]
	GetHandleVerifier [0x0x7b8883+2672035]
	GetHandleVerifier [0x0x7b3cba+2652634]
	GetHandleVerifier [0x0x562bca+223466]
	GetHandleVerifier [0x0x552cb8+158168]
	GetHandleVerifier [0x0x55978d+185517]
	GetHandleVerifier [0x0x543b78+96408]
	GetHandleVerifier [0x0x543d02+96802]
	GetHandleVerifier [0x0x52e90a+9770]
	BaseThreadInitThunk [0x0x76cafa29+25]
	RtlGetAppContainerNamedObjectPath [0x0x77dd7a9e+286]
	RtlGetAppContainerNamedObjectPath [0x0x77dd7a6e+238]
 (12.407秒)
2025-08-07 00:59:10,175 | INFO | __main__ | 🍪 使用真实Cookie测试: <EMAIL>
2025-08-07 00:59:10,177 | ERROR | __main__ | ❌ 加载Cookie失败: <EMAIL> - Expecting value: line 1 column 1 (char 0)
2025-08-07 00:59:10,179 | INFO | __main__ | 🍪 使用真实Cookie测试: <EMAIL>
2025-08-07 00:59:10,180 | ERROR | __main__ | ❌ 加载Cookie失败: <EMAIL> - Expecting value: line 1 column 1 (char 0)
2025-08-07 00:59:10,182 | INFO | __main__ | 🍪 使用真实Cookie测试: <EMAIL>
2025-08-07 00:59:10,183 | ERROR | __main__ | ❌ 加载Cookie失败: <EMAIL> - Expecting value: line 1 column 1 (char 0)
2025-08-07 01:00:46,116 | INFO | __main__ | 🌐 测试纯连接速度...
2025-08-07 01:00:46,117 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 01:00:46,117 | INFO | __main__ |    ✅ 配置了 42 个超快速优化参数
2025-08-07 01:00:46,117 | INFO | WDM | ====== WebDriver manager ======
2025-08-07 01:00:51,227 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:00:51,439 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:00:51,722 | INFO | WDM | Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver.exe] found in cache
2025-08-07 01:00:56,391 | INFO | __main__ |    ⚡ 纯连接测试: 失败 (10.254秒)
2025-08-07 01:00:58,577 | INFO | __main__ | 🌐 测试纯连接速度...
2025-08-07 01:00:58,577 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 01:00:58,578 | INFO | __main__ |    ✅ 配置了 42 个超快速优化参数
2025-08-07 01:00:58,578 | INFO | WDM | ====== WebDriver manager ======
2025-08-07 01:01:03,616 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:01:03,839 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:01:04,051 | INFO | WDM | Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver.exe] found in cache
2025-08-07 01:01:06,385 | INFO | __main__ |    ⚡ 纯连接测试: 成功 (7.798秒)
2025-08-07 01:01:08,561 | INFO | __main__ | 🌐 测试纯连接速度...
2025-08-07 01:01:08,562 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 01:01:08,563 | INFO | __main__ |    ✅ 配置了 42 个超快速优化参数
2025-08-07 01:01:08,564 | INFO | WDM | ====== WebDriver manager ======
2025-08-07 01:01:13,731 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:01:13,948 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:01:14,152 | INFO | WDM | Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver.exe] found in cache
2025-08-07 01:01:16,507 | INFO | __main__ |    ⚡ 纯连接测试: 成功 (7.936秒)
2025-08-07 01:01:18,694 | INFO | __main__ | 🍪 使用真实Cookie测试: <EMAIL>
2025-08-07 01:01:18,745 | INFO | __main__ | ✅ 解密Cookie成功: <EMAIL>
2025-08-07 01:01:18,745 | INFO | __main__ | ✅ 加载Cookie成功: <EMAIL> (8 个)
2025-08-07 01:01:18,745 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 01:01:18,746 | INFO | __main__ |    ✅ 配置了 42 个超快速优化参数
2025-08-07 01:01:18,746 | ERROR | __main__ |    ❌ 测试异常: property 'arguments' of 'Options' object has no setter
2025-08-07 01:01:18,747 | INFO | __main__ | 🍪 使用真实Cookie测试: <EMAIL>
2025-08-07 01:01:18,749 | INFO | __main__ | ✅ 解密Cookie成功: <EMAIL>
2025-08-07 01:01:18,750 | INFO | __main__ | ✅ 加载Cookie成功: <EMAIL> (8 个)
2025-08-07 01:01:18,750 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 01:01:18,751 | INFO | __main__ |    ✅ 配置了 42 个超快速优化参数
2025-08-07 01:01:18,751 | ERROR | __main__ |    ❌ 测试异常: property 'arguments' of 'Options' object has no setter
2025-08-07 01:01:18,753 | INFO | __main__ | 🍪 使用真实Cookie测试: <EMAIL>
2025-08-07 01:01:18,754 | INFO | __main__ | ✅ 解密Cookie成功: <EMAIL>
2025-08-07 01:01:18,754 | INFO | __main__ | ✅ 加载Cookie成功: <EMAIL> (8 个)
2025-08-07 01:01:18,755 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 01:01:18,755 | INFO | __main__ |    ✅ 配置了 42 个超快速优化参数
2025-08-07 01:01:18,756 | ERROR | __main__ |    ❌ 测试异常: property 'arguments' of 'Options' object has no setter
2025-08-07 01:01:39,930 | INFO | __main__ | 🌐 测试纯连接速度...
2025-08-07 01:01:39,930 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 01:01:39,930 | INFO | __main__ |    ✅ 配置了 42 个超快速优化参数
2025-08-07 01:01:39,931 | INFO | WDM | ====== WebDriver manager ======
2025-08-07 01:01:45,038 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:01:45,264 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:01:45,476 | INFO | WDM | Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver.exe] found in cache
2025-08-07 01:01:50,121 | INFO | __main__ |    ⚡ 纯连接测试: 成功 (10.175秒)
2025-08-07 01:01:52,268 | INFO | __main__ | 🌐 测试纯连接速度...
2025-08-07 01:01:52,269 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 01:01:52,269 | INFO | __main__ |    ✅ 配置了 42 个超快速优化参数
2025-08-07 01:01:52,270 | INFO | WDM | ====== WebDriver manager ======
2025-08-07 01:01:57,293 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:01:57,515 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:01:57,744 | INFO | WDM | Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver.exe] found in cache
2025-08-07 01:02:00,063 | INFO | __main__ |    ⚡ 纯连接测试: 成功 (7.785秒)
2025-08-07 01:02:02,229 | INFO | __main__ | 🌐 测试纯连接速度...
2025-08-07 01:02:02,229 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 01:02:02,230 | INFO | __main__ |    ✅ 配置了 42 个超快速优化参数
2025-08-07 01:02:02,230 | INFO | WDM | ====== WebDriver manager ======
2025-08-07 01:02:07,277 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:02:07,487 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:02:07,715 | INFO | WDM | Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver.exe] found in cache
2025-08-07 01:02:12,357 | INFO | __main__ |    ⚡ 纯连接测试: 失败 (10.106秒)
2025-08-07 01:02:14,552 | INFO | __main__ | 🍪 使用真实Cookie测试: <EMAIL>
2025-08-07 01:02:14,606 | INFO | __main__ | ✅ 解密Cookie成功: <EMAIL>
2025-08-07 01:02:14,607 | INFO | __main__ | ✅ 加载Cookie成功: <EMAIL> (8 个)
2025-08-07 01:02:14,607 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 01:02:14,608 | INFO | __main__ |    ✅ 配置了 42 个超快速优化参数
2025-08-07 01:02:14,608 | INFO | WDM | ====== WebDriver manager ======
2025-08-07 01:02:19,855 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:02:20,060 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:02:20,270 | INFO | WDM | Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver.exe] found in cache
2025-08-07 01:02:22,643 | INFO | __main__ |    🎯 测试完成: 失败 (总耗时: 8.035秒)
2025-08-07 01:02:24,824 | INFO | __main__ | 🍪 使用真实Cookie测试: <EMAIL>
2025-08-07 01:02:24,825 | INFO | __main__ | ✅ 解密Cookie成功: <EMAIL>
2025-08-07 01:02:24,825 | INFO | __main__ | ✅ 加载Cookie成功: <EMAIL> (8 个)
2025-08-07 01:02:24,826 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 01:02:24,827 | INFO | __main__ |    ✅ 配置了 42 个超快速优化参数
2025-08-07 01:02:24,827 | INFO | WDM | ====== WebDriver manager ======
2025-08-07 01:02:29,812 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:02:30,035 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:02:30,249 | INFO | WDM | Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver.exe] found in cache
2025-08-07 01:02:33,574 | INFO | __main__ |    🎯 测试完成: 失败 (总耗时: 8.748秒)
2025-08-07 01:02:35,773 | INFO | __main__ | 🍪 使用真实Cookie测试: <EMAIL>
2025-08-07 01:02:35,776 | INFO | __main__ | ✅ 解密Cookie成功: <EMAIL>
2025-08-07 01:02:35,776 | INFO | __main__ | ✅ 加载Cookie成功: <EMAIL> (8 个)
2025-08-07 01:02:35,777 | INFO | __main__ | 🚀 创建超快速Chrome选项...
2025-08-07 01:02:35,778 | INFO | __main__ |    ✅ 配置了 42 个超快速优化参数
2025-08-07 01:02:35,778 | INFO | WDM | ====== WebDriver manager ======
2025-08-07 01:02:40,939 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:02:41,142 | INFO | WDM | Get LATEST chromedriver version for google-chrome
2025-08-07 01:02:41,350 | INFO | WDM | Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver.exe] found in cache
2025-08-07 01:02:55,800 | INFO | __main__ |    🎯 测试完成: 失败 (总耗时: 20.023秒)
