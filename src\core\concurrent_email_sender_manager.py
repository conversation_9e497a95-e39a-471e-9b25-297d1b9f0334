#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并发邮件发送管理器
实现真正的多浏览器并发发送，每个浏览器独立线程，支持智能任务分配和账号切换
"""

import time
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, Future
from queue import Queue, Empty
from loguru import logger

from selenium import webdriver
from selenium.webdriver.chrome.options import Options

from src.models.account import Account
from .smart_account_strategy import SmartAccountStrategy, AccountStrategy, BrowserMode, SendingMode
from .intelligent_task_distributor import IntelligentTaskDistributor, BrowserTaskQueue, EmailTask
from .enhanced_cookie_account_switcher import EnhancedCookieAccountSwitcher, AccountSwitchResult
from .sina_ultra_fast_sender_final import SinaUltraFastSenderFinal


@dataclass
class BrowserWorker:
    """浏览器工作器"""
    browser_id: int
    driver: Optional[webdriver.Chrome] = None
    task_queue: Optional[BrowserTaskQueue] = None
    account_switcher: Optional[EnhancedCookieAccountSwitcher] = None
    sender: Optional[SinaUltraFastSenderFinal] = None
    worker_thread: Optional[threading.Thread] = None
    is_running: bool = False
    is_paused: bool = False
    
    # 统计信息
    total_sent: int = 0
    total_failed: int = 0
    current_account_email: Optional[str] = None
    last_send_time: float = 0
    start_time: float = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'browser_id': self.browser_id,
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'total_sent': self.total_sent,
            'total_failed': self.total_failed,
            'current_account': self.current_account_email,
            'last_send_time': self.last_send_time,
            'uptime': time.time() - self.start_time if self.start_time > 0 else 0,
            'pending_tasks': len(self.task_queue.task_queue) if self.task_queue else 0
        }


@dataclass
class ConcurrentSendingConfig:
    """并发发送配置"""
    max_browsers: int = 10
    send_interval: float = 2.0
    account_switch_threshold: int = 10
    retry_attempts: int = 3
    browser_timeout: int = 60
    enable_headless: bool = False
    user_data_dir_base: str = "chrome_profiles"


class ConcurrentEmailSenderManager:
    """并发邮件发送管理器"""
    
    def __init__(self, config: ConcurrentSendingConfig):
        self.config = config
        self.logger = logger
        
        # 核心组件
        self.strategy_analyzer = SmartAccountStrategy()
        self.task_distributor = IntelligentTaskDistributor()
        
        # 浏览器工作器管理
        self.browser_workers: Dict[int, BrowserWorker] = {}
        self.thread_pool: Optional[ThreadPoolExecutor] = None
        
        # 发送状态
        self.is_sending = False
        self.is_paused = False
        self.start_time = 0
        
        # 回调函数
        self.progress_callback: Optional[Callable] = None
        self.completion_callback: Optional[Callable] = None
        self.error_callback: Optional[Callable] = None
        
        # 统计信息
        self.global_stats = {
            'total_browsers': 0,
            'active_browsers': 0,
            'total_sent': 0,
            'total_failed': 0,
            'start_time': 0,
            'emails_per_minute': 0
        }
    
    def start_concurrent_sending(
        self,
        accounts: List[Account],
        recipient_emails: List[str],
        subject: str,
        content: str,
        content_type: str = "text/plain",
        cc_email: Optional[str] = None,
        sending_mode: SendingMode = SendingMode.INDIVIDUAL,
        batch_size: int = 1,
        user_browser_count: Optional[int] = None
    ) -> bool:
        """
        启动并发发送
        
        Args:
            accounts: 可用账号列表
            recipient_emails: 收件人邮箱列表
            subject: 邮件主题
            content: 邮件内容
            content_type: 内容类型
            cc_email: 抄送邮箱
            sending_mode: 发送模式
            batch_size: 批量大小
            user_browser_count: 用户指定的浏览器数量
            
        Returns:
            bool: 启动是否成功
        """
        try:
            if self.is_sending:
                self.logger.warning("发送器已在运行中")
                return False
            
            self.logger.info(f"🚀 启动并发邮件发送...")
            self.logger.info(f"   - 账号数量：{len(accounts)}")
            self.logger.info(f"   - 收件人数量：{len(recipient_emails)}")
            self.logger.info(f"   - 发送模式：{sending_mode.value}")
            
            # 1. 分析发送策略
            strategy = self.strategy_analyzer.analyze_sending_strategy(
                available_accounts=accounts,
                recipient_emails=recipient_emails,
                sending_mode=sending_mode,
                user_browser_count=user_browser_count,
                emails_per_account=self.config.account_switch_threshold
            )
            
            # 2. 分配任务
            browser_queues = self.task_distributor.distribute_tasks(
                strategy=strategy,
                recipient_emails=recipient_emails,
                subject=subject,
                content=content,
                content_type=content_type,
                cc_email=cc_email,
                sending_mode=sending_mode,
                batch_size=batch_size
            )
            
            # 3. 初始化浏览器工作器
            if not self._initialize_browser_workers(strategy, browser_queues):
                self.logger.error("初始化浏览器工作器失败")
                return False
            
            # 4. 启动并发发送
            self._start_concurrent_workers()
            
            self.is_sending = True
            self.start_time = time.time()
            self.global_stats['start_time'] = self.start_time
            self.global_stats['total_browsers'] = len(self.browser_workers)
            
            self.logger.info(f"✅ 并发发送启动成功，{len(self.browser_workers)}个浏览器开始工作")
            return True
            
        except Exception as e:
            self.logger.error(f"启动并发发送失败: {e}")
            self._cleanup_resources()
            return False
    
    def _initialize_browser_workers(
        self,
        strategy: AccountStrategy,
        browser_queues: Dict[int, BrowserTaskQueue]
    ) -> bool:
        """初始化浏览器工作器"""
        try:
            self.logger.info(f"🔧 初始化{strategy.browser_count}个浏览器工作器...")
            
            for browser_id in range(strategy.browser_count):
                if browser_id not in browser_queues:
                    self.logger.warning(f"浏览器{browser_id}没有分配到任务队列")
                    continue
                
                # 创建浏览器
                driver = self._create_browser_driver(browser_id)
                if not driver:
                    self.logger.error(f"创建浏览器{browser_id}失败")
                    return False
                
                # 创建账号切换器
                account_switcher = EnhancedCookieAccountSwitcher()
                task_queue = browser_queues[browser_id]
                
                # 初始化账号管理
                if not account_switcher.initialize_browser_accounts(
                    browser_id, driver, task_queue.assigned_accounts
                ):
                    self.logger.error(f"初始化浏览器{browser_id}账号管理失败")
                    driver.quit()
                    return False
                
                # 创建发送器
                sender = SinaUltraFastSenderFinal(driver)
                
                # 创建工作器
                worker = BrowserWorker(
                    browser_id=browser_id,
                    driver=driver,
                    task_queue=task_queue,
                    account_switcher=account_switcher,
                    sender=sender,
                    start_time=time.time()
                )
                
                self.browser_workers[browser_id] = worker
                self.logger.info(f"✅ 浏览器{browser_id}工作器初始化完成")
            
            return len(self.browser_workers) > 0
            
        except Exception as e:
            self.logger.error(f"初始化浏览器工作器失败: {e}")
            return False
    
    def _create_browser_driver(self, browser_id: int) -> Optional[webdriver.Chrome]:
        """创建浏览器驱动"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.logger.info(f"🔧 正在创建浏览器{browser_id} (尝试 {attempt + 1}/{max_retries})...")

                # 清理可能的残留进程
                self._cleanup_chrome_processes()

                chrome_options = Options()

                # 使用经过测试的稳定配置
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--headless=new')
                chrome_options.add_argument('--disable-extensions')
                chrome_options.add_argument('--disable-plugins')
                chrome_options.add_argument('--no-first-run')
                chrome_options.add_argument('--disable-default-apps')
                chrome_options.add_argument('--disable-popup-blocking')
                chrome_options.add_argument('--disable-translate')
                chrome_options.add_argument('--disable-background-networking')
                chrome_options.add_argument('--disable-sync')
                chrome_options.add_argument('--log-level=3')

                # 为每个浏览器分配不同的调试端口
                debug_port = 9222 + browser_id
                chrome_options.add_argument(f'--remote-debugging-port={debug_port}')

                # 反检测配置
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)

                # 用户数据目录（每个浏览器独立）
                import os
                import tempfile
                user_data_dir = os.path.join(tempfile.gettempdir(), f"chrome_browser_{browser_id}_{int(time.time())}")
                os.makedirs(user_data_dir, exist_ok=True)
                chrome_options.add_argument(f'--user-data-dir={user_data_dir}')

                # 创建服务
                from selenium.webdriver.chrome.service import Service
                service = Service()

                # 创建驱动
                driver = webdriver.Chrome(service=service, options=chrome_options)
                driver.set_page_load_timeout(self.config.browser_timeout)
                driver.implicitly_wait(10)

                # 设置反检测
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

                self.logger.info(f"✅ 浏览器{browser_id}创建成功")
                return driver

            except Exception as e:
                self.logger.warning(f"⚠️ 浏览器{browser_id}创建失败 (尝试 {attempt + 1}/{max_retries}): {e}")

                if attempt < max_retries - 1:
                    # 等待一段时间后重试
                    time.sleep(2 * (attempt + 1))
                    continue
                else:
                    self.logger.error(f"❌ 浏览器{browser_id}创建最终失败")
                    return None

        return None

    def _cleanup_chrome_processes(self):
        """清理Chrome残留进程"""
        try:
            import psutil
            import subprocess

            # 使用taskkill命令强制结束Chrome进程
            try:
                subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe'],
                             capture_output=True, timeout=5)
                subprocess.run(['taskkill', '/F', '/IM', 'chromedriver.exe'],
                             capture_output=True, timeout=5)
            except:
                pass

            # 使用psutil清理
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        proc.kill()
                except:
                    pass

        except Exception as e:
            self.logger.debug(f"清理Chrome进程时出错: {e}")
            pass
    
    def _start_concurrent_workers(self):
        """启动并发工作器"""
        try:
            # 创建线程池
            self.thread_pool = ThreadPoolExecutor(
                max_workers=len(self.browser_workers),
                thread_name_prefix="EmailSender"
            )
            
            # 启动每个浏览器工作器
            for browser_id, worker in self.browser_workers.items():
                worker.is_running = True
                worker.worker_thread = self.thread_pool.submit(
                    self._browser_worker_loop, worker
                )
                self.logger.info(f"启动浏览器{browser_id}工作线程")
            
            # 启动监控线程
            monitor_thread = threading.Thread(
                target=self._monitor_progress,
                name="ProgressMonitor",
                daemon=True
            )
            monitor_thread.start()
            
        except Exception as e:
            self.logger.error(f"启动并发工作器失败: {e}")
            raise
    
    def _browser_worker_loop(self, worker: BrowserWorker):
        """浏览器工作器主循环"""
        try:
            self.logger.info(f"🔄 浏览器{worker.browser_id}工作循环开始")
            
            while worker.is_running and not self._should_stop():
                try:
                    # 检查暂停状态
                    if worker.is_paused or self.is_paused:
                        time.sleep(1)
                        continue
                    
                    # 获取下一个任务
                    task = self._get_next_task(worker)
                    if not task:
                        # 没有更多任务，等待或结束
                        if self._all_tasks_completed(worker):
                            break
                        time.sleep(1)
                        continue
                    
                    # 检查是否需要切换账号
                    if self._should_switch_account(worker, task):
                        self._switch_account(worker, task)
                    
                    # 发送邮件
                    success = self._send_email_task(worker, task)
                    
                    # 更新统计
                    if success:
                        worker.total_sent += 1
                        self.global_stats['total_sent'] += 1
                        task.status = "completed"
                    else:
                        worker.total_failed += 1
                        self.global_stats['total_failed'] += 1
                        task.status = "failed"
                    
                    worker.last_send_time = time.time()
                    
                    # 发送间隔
                    time.sleep(self.config.send_interval)
                    
                except Exception as e:
                    self.logger.error(f"浏览器{worker.browser_id}工作循环异常: {e}")
                    time.sleep(5)  # 异常后等待一段时间
            
            self.logger.info(f"✅ 浏览器{worker.browser_id}工作循环结束")
            
        except Exception as e:
            self.logger.error(f"浏览器{worker.browser_id}工作循环严重异常: {e}")
        finally:
            worker.is_running = False
    
    def _get_next_task(self, worker: BrowserWorker) -> Optional[EmailTask]:
        """获取下一个任务"""
        if not worker.task_queue or not worker.task_queue.task_queue:
            return None
        
        # 查找待处理的任务
        for task in worker.task_queue.task_queue:
            if task.status == "pending":
                task.status = "processing"
                return task
        
        return None
    
    def _should_switch_account(self, worker: BrowserWorker, task: EmailTask) -> bool:
        """判断是否需要切换账号"""
        if not worker.task_queue:
            return False
        
        return worker.task_queue.should_switch_account(self.config.account_switch_threshold)
    
    def _switch_account(self, worker: BrowserWorker, task: EmailTask):
        """切换账号"""
        try:
            if not worker.task_queue or not worker.account_switcher:
                return
            
            # 获取下一个账号
            next_account = worker.task_queue.get_current_account()
            if not next_account:
                return
            
            # 执行切换
            result = worker.account_switcher.switch_account(
                worker.browser_id, worker.driver, next_account
            )
            
            if result.success:
                worker.current_account_email = next_account.email
                worker.task_queue.switch_to_next_account()
                self.logger.info(f"浏览器{worker.browser_id}切换账号成功: {next_account.email}")
            else:
                self.logger.error(f"浏览器{worker.browser_id}切换账号失败: {result.error_message}")
                
        except Exception as e:
            self.logger.error(f"浏览器{worker.browser_id}切换账号异常: {e}")
    
    def _send_email_task(self, worker: BrowserWorker, task: EmailTask) -> bool:
        """发送邮件任务"""
        try:
            if not worker.sender:
                return False
            
            # 发送邮件
            if len(task.to_emails) == 1:
                # 单个收件人
                success = worker.sender.send_email(
                    to_email=task.to_emails[0],
                    subject=task.subject,
                    content=task.content,
                    content_type=task.content_type
                )
            else:
                # 批量收件人
                success = worker.sender.send_batch_email(
                    to_emails=task.to_emails,
                    subject=task.subject,
                    content=task.content,
                    content_type=task.content_type
                )
            
            if success:
                self.logger.info(f"浏览器{worker.browser_id}发送成功: {task.to_emails}")

                # 更新账号发送计数（内存）
                if worker.task_queue and worker.current_account_email:
                    worker.task_queue.increment_send_count(worker.current_account_email)

                # 更新账号发送计数到数据库
                if worker.current_account_email:
                    try:
                        from src.models.account import AccountManager
                        from src.models.database import DatabaseManager

                        db_manager = DatabaseManager()
                        account_manager = AccountManager(db_manager)

                        # 根据邮箱获取账号ID并更新发送次数
                        account = account_manager.get_account_by_email(worker.current_account_email)
                        if account and account.id:
                            account_manager.increment_send_count(account.id)
                            self.logger.info(f"✅ 已更新账号发送次数: {account.email}")
                    except Exception as e:
                        self.logger.error(f"❌ 更新账号发送次数失败: {e}")
            else:
                self.logger.warning(f"浏览器{worker.browser_id}发送失败: {task.to_emails}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"浏览器{worker.browser_id}发送邮件异常: {e}")
            return False

    def _all_tasks_completed(self, worker: BrowserWorker) -> bool:
        """检查是否所有任务都已完成"""
        if not worker.task_queue:
            return True

        # 检查是否还有待处理的任务
        pending_tasks = [task for task in worker.task_queue.task_queue if task.status == "pending"]
        return len(pending_tasks) == 0

    def _should_stop(self) -> bool:
        """检查是否应该停止发送"""
        return not self.is_sending

    def _monitor_progress(self):
        """监控发送进度"""
        try:
            while self.is_sending:
                # 更新全局统计
                self._update_global_stats()

                # 检查是否所有任务都完成
                if self._all_browsers_completed():
                    self.logger.info("🎉 所有浏览器任务完成，停止发送")
                    self.stop_sending()
                    break

                # 调用进度回调
                if self.progress_callback:
                    try:
                        self.progress_callback(self.get_sending_stats())
                    except Exception as e:
                        self.logger.error(f"进度回调异常: {e}")

                time.sleep(2)  # 每2秒更新一次

        except Exception as e:
            self.logger.error(f"进度监控异常: {e}")

    def _update_global_stats(self):
        """更新全局统计"""
        try:
            active_browsers = sum(1 for worker in self.browser_workers.values() if worker.is_running)
            self.global_stats['active_browsers'] = active_browsers

            # 计算发送速率
            if self.start_time > 0:
                elapsed_minutes = (time.time() - self.start_time) / 60
                if elapsed_minutes > 0:
                    self.global_stats['emails_per_minute'] = self.global_stats['total_sent'] / elapsed_minutes

        except Exception as e:
            self.logger.error(f"更新全局统计异常: {e}")

    def _all_browsers_completed(self) -> bool:
        """检查是否所有浏览器都完成了任务"""
        for worker in self.browser_workers.values():
            if worker.is_running and not self._all_tasks_completed(worker):
                return False
        return True

    def stop_sending(self):
        """停止发送"""
        try:
            self.logger.info("🛑 停止并发发送...")

            self.is_sending = False

            # 停止所有工作器
            for worker in self.browser_workers.values():
                worker.is_running = False

            # 等待线程结束
            if self.thread_pool:
                self.thread_pool.shutdown(wait=True, timeout=30)

            # 清理资源
            self._cleanup_resources()

            # 调用完成回调
            if self.completion_callback:
                try:
                    self.completion_callback(self.get_sending_stats())
                except Exception as e:
                    self.logger.error(f"完成回调异常: {e}")

            self.logger.info("✅ 并发发送已停止")

        except Exception as e:
            self.logger.error(f"停止发送异常: {e}")

    def pause_sending(self):
        """暂停发送"""
        try:
            self.logger.info("⏸️ 暂停并发发送...")
            self.is_paused = True

            for worker in self.browser_workers.values():
                worker.is_paused = True

        except Exception as e:
            self.logger.error(f"暂停发送异常: {e}")

    def resume_sending(self):
        """恢复发送"""
        try:
            self.logger.info("▶️ 恢复并发发送...")
            self.is_paused = False

            for worker in self.browser_workers.values():
                worker.is_paused = False

        except Exception as e:
            self.logger.error(f"恢复发送异常: {e}")

    def _cleanup_resources(self):
        """清理资源"""
        try:
            # 关闭所有浏览器
            for worker in self.browser_workers.values():
                if worker.driver:
                    try:
                        worker.driver.quit()
                    except Exception as e:
                        self.logger.error(f"关闭浏览器{worker.browser_id}异常: {e}")

            # 清空工作器
            self.browser_workers.clear()

            # 关闭线程池
            if self.thread_pool:
                self.thread_pool.shutdown(wait=False)
                self.thread_pool = None

        except Exception as e:
            self.logger.error(f"清理资源异常: {e}")

    def get_sending_stats(self) -> Dict[str, Any]:
        """获取发送统计信息"""
        try:
            # 浏览器统计
            browser_stats = {}
            for browser_id, worker in self.browser_workers.items():
                browser_stats[browser_id] = worker.get_stats()

            # 任务统计
            total_tasks = sum(
                len(worker.task_queue.task_queue) if worker.task_queue else 0
                for worker in self.browser_workers.values()
            )

            completed_tasks = sum(
                len([task for task in worker.task_queue.task_queue if task.status == "completed"])
                if worker.task_queue else 0
                for worker in self.browser_workers.values()
            )

            pending_tasks = sum(
                len([task for task in worker.task_queue.task_queue if task.status == "pending"])
                if worker.task_queue else 0
                for worker in self.browser_workers.values()
            )

            # 综合统计
            stats = {
                'global_stats': self.global_stats.copy(),
                'browser_stats': browser_stats,
                'task_stats': {
                    'total_tasks': total_tasks,
                    'completed_tasks': completed_tasks,
                    'pending_tasks': pending_tasks,
                    'failed_tasks': self.global_stats['total_failed']
                },
                'status': {
                    'is_sending': self.is_sending,
                    'is_paused': self.is_paused,
                    'active_browsers': self.global_stats['active_browsers'],
                    'total_browsers': self.global_stats['total_browsers']
                }
            }

            return stats

        except Exception as e:
            self.logger.error(f"获取发送统计异常: {e}")
            return {}

    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback

    def set_completion_callback(self, callback: Callable):
        """设置完成回调函数"""
        self.completion_callback = callback

    def set_error_callback(self, callback: Callable):
        """设置错误回调函数"""
        self.error_callback = callback
