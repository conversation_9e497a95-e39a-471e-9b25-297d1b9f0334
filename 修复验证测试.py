#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复验证测试脚本
测试修复后的浏览器创建和驱动验证功能
"""

import sys
import os
import time
sys.path.insert(0, '.')

def test_browser_creation_fix():
    """测试修复后的浏览器创建"""
    print("🔧 测试修复后的浏览器创建...")
    
    try:
        from src.core.browser_manager import BrowserManager
        from src.models.account import AccountManager
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        # 获取可用账号
        account_manager = AccountManager(db_manager)
        available_accounts = account_manager.get_available_accounts()
        
        if not available_accounts:
            print("   ❌ 没有可用账号")
            return False, None
        
        test_account = available_accounts[0]
        print(f"   🧪 测试账号: {test_account.email}")
        
        # 创建浏览器管理器
        config_dict = getattr(config, 'config', {})
        browser_config = config_dict.get('browser', {})
        browser_config['window_size'] = [1280, 720]
        browser_config['window_mode'] = '正常窗口'  # 使用正常窗口便于观察
        config_dict['browser'] = browser_config
        
        browser_manager = BrowserManager(config_dict)
        print("   ✅ 浏览器管理器创建成功")
        
        # 创建浏览器驱动（使用修复后的方法）
        print(f"   🚀 创建浏览器驱动（修复版）...")
        driver_id = browser_manager.create_driver(test_account)
        
        if driver_id:
            print(f"   ✅ 浏览器驱动创建成功: {driver_id}")
            
            # 获取驱动并检查状态
            driver = browser_manager.get_driver(driver_id)
            if driver:
                print("   ✅ 浏览器驱动获取成功")
                
                # 等待页面加载
                time.sleep(3)
                
                # 检查驱动是否存活
                try:
                    current_url = driver.current_url
                    page_title = driver.title
                    print(f"   📊 当前URL: {current_url}")
                    print(f"   📊 页面标题: {page_title}")
                    
                    # 测试驱动存活性
                    print("   🔍 测试驱动存活性...")
                    try:
                        test_url = driver.current_url
                        print("   ✅ 驱动存活性测试通过")
                        driver_alive = True
                    except Exception as e:
                        print(f"   ❌ 驱动存活性测试失败: {e}")
                        driver_alive = False
                    
                    return driver_alive, driver
                    
                except Exception as e:
                    print(f"   ❌ 检查浏览器状态失败: {e}")
                    return False, None
            else:
                print("   ❌ 浏览器驱动获取失败")
                return False, None
        else:
            print("   ❌ 浏览器驱动创建失败")
            return False, None
            
    except Exception as e:
        print(f"   ❌ 浏览器创建测试失败: {e}")
        return False, None

def test_driver_validation():
    """测试驱动验证功能"""
    print("\n🔧 测试驱动验证功能...")
    
    try:
        # 创建一个模拟的驱动列表
        driver_alive, driver = test_browser_creation_fix()
        
        if not driver:
            print("   ❌ 没有可用的驱动进行测试")
            return False
        
        # 测试驱动验证函数
        def is_driver_alive(driver):
            """检查驱动是否存活"""
            try:
                # 尝试获取当前URL来测试驱动是否可用
                driver.current_url
                return True
            except Exception:
                return False
        
        print("   🔍 测试驱动验证函数...")
        alive = is_driver_alive(driver)
        
        if alive:
            print("   ✅ 驱动验证通过")
            return True
        else:
            print("   ❌ 驱动验证失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 驱动验证测试失败: {e}")
        return False

def test_multi_browser_widget():
    """测试多浏览器组件"""
    print("\n🔧 测试多浏览器组件...")
    
    try:
        from src.gui.optimized_multi_browser_widget import OptimizedMultiBrowserWidget
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        from PyQt5.QtWidgets import QApplication
        import sys
        
        # 创建QApplication（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        # 创建多浏览器组件
        browser_widget = OptimizedMultiBrowserWidget(db_manager)
        print("   ✅ 多浏览器组件创建成功")
        
        # 设置浏览器数量为1
        browser_widget.browser_count_spin.setValue(1)
        
        # 测试浏览器创建
        print("   🚀 测试浏览器创建...")
        browser_widget._create_browser_drivers()
        
        # 检查创建结果
        if hasattr(browser_widget, 'browser_drivers') and browser_widget.browser_drivers:
            print(f"   ✅ 成功创建 {len(browser_widget.browser_drivers)} 个浏览器")
            
            # 测试驱动验证
            print("   🔍 测试驱动验证...")
            validation_result = browser_widget._validate_browser_drivers()
            
            if validation_result:
                print("   ✅ 驱动验证通过")
                return True
            else:
                print("   ❌ 驱动验证失败")
                return False
        else:
            print("   ❌ 没有创建任何浏览器")
            return False
            
    except Exception as e:
        print(f"   ❌ 多浏览器组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 修复验证测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("浏览器创建修复测试", test_browser_creation_fix),
        ("驱动验证功能测试", test_driver_validation),
        ("多浏览器组件测试", test_multi_browser_widget)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            if test_name == "浏览器创建修复测试":
                result, driver = test_func()
                results.append((test_name, result))
                if driver:
                    # 等待观察
                    print("\n⏳ 等待5秒供观察...")
                    time.sleep(5)
                    try:
                        driver.quit()
                        print("✅ 浏览器清理成功")
                    except:
                        pass
            else:
                result = test_func()
                results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 修复验证全部通过！")
        print("\n💡 修复效果:")
        print("   ✅ 浏览器创建不再因登录失败而断开")
        print("   ✅ 驱动验证功能正常工作")
        print("   ✅ 多浏览器组件可以正常启动")
        print("\n🚀 系统现在应该能够正常启动发送功能！")
    elif passed >= 2:
        print("🎯 大部分功能正常！")
        print("   核心修复已生效")
    else:
        print("⚠️ 仍有问题需要进一步调试")
    
    return passed >= 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
