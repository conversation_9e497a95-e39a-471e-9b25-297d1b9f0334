#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器测试脚本
测试OptimizedMultiBrowserWidget的配置管理器是否正确初始化
"""

import sys
import os
sys.path.insert(0, '.')

def test_widget_config():
    """测试多浏览器发送器的配置管理器"""
    print("🔧 测试多浏览器发送器的配置管理器...")
    
    try:
        from src.gui.optimized_multi_browser_widget import OptimizedMultiBrowserWidget
        from src.models.database import DatabaseManager
        
        # 创建组件
        print("   🚀 创建OptimizedMultiBrowserWidget...")
        widget = OptimizedMultiBrowserWidget()
        
        # 检查配置管理器
        print("📋 配置管理器检查:")
        
        has_config = hasattr(widget, 'config')
        print(f"   📊 has_config: {has_config}")
        
        if has_config:
            config_type = type(widget.config)
            print(f"   📊 config_type: {config_type}")
            
            # 测试配置方法
            try:
                db_path = widget.config.get_database_path()
                print(f"   ✅ get_database_path(): {db_path}")
            except Exception as e:
                print(f"   ❌ get_database_path() 失败: {e}")
            
            try:
                config_dict = widget.config.get_all()
                print(f"   ✅ get_all(): 配置项数量 {len(config_dict)}")
            except Exception as e:
                print(f"   ❌ get_all() 失败: {e}")
        
        # 检查数据库管理器
        print("\n📋 数据库管理器检查:")
        
        has_db_manager = hasattr(widget, 'db_manager')
        print(f"   📊 has_db_manager: {has_db_manager}")
        
        if has_db_manager:
            db_manager_type = type(widget.db_manager)
            print(f"   📊 db_manager_type: {db_manager_type}")
        
        # 测试浏览器管理器创建
        print("\n📋 浏览器管理器创建测试:")
        
        try:
            from src.core.browser_manager import BrowserManager
            browser_manager = BrowserManager(widget.config, widget.db_manager)
            print("   ✅ BrowserManager创建成功")
            return True
        except Exception as e:
            print(f"   ❌ BrowserManager创建失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_browser_driver_creation():
    """测试浏览器驱动创建流程"""
    print("\n🔧 测试浏览器驱动创建流程...")
    
    try:
        from src.gui.optimized_multi_browser_widget import OptimizedMultiBrowserWidget
        
        # 创建组件
        widget = OptimizedMultiBrowserWidget()
        
        print("📋 浏览器驱动创建流程测试:")
        
        # 检查必要属性
        checks = {
            "config": hasattr(widget, 'config'),
            "db_manager": hasattr(widget, 'db_manager'),
            "browser_count_spin": hasattr(widget, 'browser_count_spin')
        }
        
        for attr_name, exists in checks.items():
            status = "✅" if exists else "❌"
            print(f"   {status} {attr_name}: {'存在' if exists else '不存在'}")
        
        if not all(checks.values()):
            print("   ⚠️ 缺少必要属性，无法继续测试")
            return False
        
        # 模拟浏览器数量设置
        if hasattr(widget, 'browser_count_spin'):
            # 如果有browser_count_spin，获取其值
            try:
                browser_count = widget.browser_count_spin.value()
                print(f"   📊 浏览器数量: {browser_count}")
            except:
                print("   📊 浏览器数量: 使用默认值 1")
                browser_count = 1
        else:
            browser_count = 1
        
        # 测试_create_browser_drivers方法是否存在
        has_create_method = hasattr(widget, '_create_browser_drivers')
        print(f"   📊 _create_browser_drivers方法: {'存在' if has_create_method else '不存在'}")
        
        # 测试_setup_sender_factory方法是否存在
        has_setup_method = hasattr(widget, '_setup_sender_factory')
        print(f"   📊 _setup_sender_factory方法: {'存在' if has_setup_method else '不存在'}")
        
        return has_create_method and has_setup_method
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_sender_factory_setup():
    """测试发送器工厂设置流程"""
    print("\n🔧 测试发送器工厂设置流程...")
    
    try:
        from src.gui.optimized_multi_browser_widget import OptimizedMultiBrowserWidget
        from src.core.email_sending_manager import EmailSendingManager, SendingConfig
        
        # 创建组件
        widget = OptimizedMultiBrowserWidget()
        
        # 创建邮件发送管理器
        sending_config = SendingConfig()
        task_sending_manager = EmailSendingManager(sending_config)
        widget.task_sending_manager = task_sending_manager
        
        print("📋 发送器工厂设置流程测试:")
        
        # 检查初始状态
        has_factory = hasattr(task_sending_manager, 'sender_factory') and task_sending_manager.sender_factory is not None
        print(f"   📊 初始发送器工厂状态: {'已设置' if has_factory else '未设置'}")
        
        # 检查设置方法
        has_setup_method = hasattr(widget, '_setup_sender_factory')
        print(f"   📊 _setup_sender_factory方法: {'存在' if has_setup_method else '不存在'}")
        
        if has_setup_method:
            print("   ✅ 发送器工厂设置流程完整")
            return True
        else:
            print("   ❌ 缺少发送器工厂设置方法")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 配置管理器和浏览器驱动创建测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("配置管理器", test_widget_config),
        ("浏览器驱动创建", test_browser_driver_creation),
        ("发送器工厂设置", test_sender_factory_setup)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 配置管理器修复成功！")
        print("\n💡 现在应该能够:")
        print("   1. 正确创建浏览器管理器")
        print("   2. 成功创建浏览器驱动")
        print("   3. 正常设置发送器工厂")
        print("   4. 启动557个任务的发送")
    else:
        print("⚠️ 部分功能仍有问题")
        print("\n💡 建议检查:")
        print("   1. 配置管理器的初始化")
        print("   2. 浏览器管理器的创建")
        print("   3. 发送器工厂的设置流程")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
