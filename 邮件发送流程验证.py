#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件发送流程验证脚本
全面检查邮件发送的完整流程，识别和修复潜在问题
"""

import sys
import os
sys.path.insert(0, '.')

def test_email_sending_manager():
    """测试邮件发送管理器"""
    print("🔧 测试邮件发送管理器...")
    
    try:
        from src.core.email_sending_manager import EmailSendingManager, SendingConfig
        
        print("📋 邮件发送管理器测试:")
        
        # 创建发送配置
        config = SendingConfig()
        manager = EmailSendingManager(config)
        
        # 检查初始状态
        status = manager.get_sending_status()
        print(f"   📊 初始状态: {status['status']}")
        print(f"   📊 任务队列: {status['queue_stats']['total_tasks']} 个任务")
        
        # 检查发送器工厂
        has_factory = hasattr(manager, 'sender_factory') and manager.sender_factory is not None
        print(f"   📊 发送器工厂: {'已设置' if has_factory else '未设置'}")
        
        # 测试设置发送器工厂
        def test_factory():
            return "test_sender"
        
        manager.set_sender_factory(test_factory)
        has_factory_after = hasattr(manager, 'sender_factory') and manager.sender_factory is not None
        print(f"   📊 设置后发送器工厂: {'已设置' if has_factory_after else '未设置'}")
        
        return has_factory_after
        
    except Exception as e:
        print(f"❌ 邮件发送管理器测试失败: {e}")
        return False

def test_task_queue_system():
    """测试任务队列系统"""
    print("\n🔧 测试任务队列系统...")
    
    try:
        from src.core.smart_task_queue import SmartTaskQueue, QueueConfig
        from src.utils.config_manager import ConfigManager
        
        print("📋 任务队列系统测试:")
        
        # 初始化配置
        config_manager = ConfigManager()
        config_manager.load_config()
        db_path = config_manager.get('database.path', 'data/sina_email.db')
        
        # 创建任务队列
        queue_config = QueueConfig()
        task_queue = SmartTaskQueue(queue_config, db_path)
        
        # 检查队列状态
        status = task_queue.get_queue_status()
        print(f"   📊 队列状态: {status}")
        
        # 测试创建批次
        batch_id = task_queue.create_batch("测试批次", 10)
        if batch_id:
            print(f"   ✅ 批次创建成功: {batch_id}")
            
            # 测试添加任务
            test_tasks = [
                {
                    'recipient_email': '<EMAIL>',
                    'subject': '测试邮件1',
                    'content': '这是测试内容1',
                    'priority': 'normal'
                },
                {
                    'recipient_email': '<EMAIL>',
                    'subject': '测试邮件2',
                    'content': '这是测试内容2',
                    'priority': 'high'
                }
            ]
            
            success = task_queue.add_tasks_to_batch(batch_id, test_tasks)
            if success:
                print(f"   ✅ 任务添加成功: {len(test_tasks)} 个任务")
                
                # 检查更新后的状态
                status_after = task_queue.get_queue_status()
                print(f"   📊 更新后队列状态: {status_after}")
                
                return True
            else:
                print("   ❌ 任务添加失败")
                return False
        else:
            print("   ❌ 批次创建失败")
            return False
        
    except Exception as e:
        print(f"❌ 任务队列系统测试失败: {e}")
        return False

def test_unified_email_sender():
    """测试统一邮件发送器"""
    print("\n🔧 测试统一邮件发送器...")
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender, SendingStrategy
        
        print("📋 统一邮件发送器测试:")
        
        # 测试不同策略的发送器创建
        strategies = [
            SendingStrategy.ULTRA_FAST,
            SendingStrategy.FAST,
            SendingStrategy.NORMAL,
            SendingStrategy.SAFE
        ]
        
        for strategy in strategies:
            try:
                sender = UnifiedEmailSender(
                    driver=None,  # 测试时不需要实际驱动
                    strategy=strategy
                )
                print(f"   ✅ {strategy.value} 策略发送器创建成功")
            except Exception as e:
                print(f"   ❌ {strategy.value} 策略发送器创建失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 统一邮件发送器测试失败: {e}")
        return False

def test_account_management():
    """测试账号管理"""
    print("\n🔧 测试账号管理...")
    
    try:
        from src.models.account import AccountManager
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        print("📋 账号管理测试:")
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        # 创建账号管理器
        account_manager = AccountManager(db_manager)
        
        # 获取所有账号
        all_accounts = account_manager.get_all_accounts()
        print(f"   📊 总账号数: {len(all_accounts)}")
        
        # 获取可用账号
        available_accounts = account_manager.get_available_accounts()
        print(f"   📊 可用账号数: {len(available_accounts)}")
        
        # 检查账号状态
        if available_accounts:
            for i, account in enumerate(available_accounts[:3]):  # 只显示前3个
                status = account.get_status_display()
                last_used = "从未使用" if account.last_used is None else account.last_used.strftime("%Y-%m-%d %H:%M:%S")
                print(f"   📧 账号 {i+1}: {account.email} - {status} (最后使用: {last_used})")
        
        return len(available_accounts) > 0
        
    except Exception as e:
        print(f"❌ 账号管理测试失败: {e}")
        return False

def test_integration_flow():
    """测试集成流程"""
    print("\n🔧 测试集成流程...")
    
    try:
        from src.core.email_sending_manager import EmailSendingManager, SendingConfig
        from src.core.unified_email_sender import UnifiedEmailSender, SendingStrategy
        from src.models.account import AccountManager
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        print("📋 集成流程测试:")
        
        # 1. 初始化所有组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        account_manager = AccountManager(db_manager)
        available_accounts = account_manager.get_available_accounts()
        
        if not available_accounts:
            print("   ❌ 没有可用账号")
            return False
        
        print(f"   ✅ 组件初始化完成，{len(available_accounts)} 个可用账号")
        
        # 2. 创建邮件发送管理器
        sending_config = SendingConfig()
        email_manager = EmailSendingManager(sending_config)
        
        # 3. 设置发送器工厂
        def create_test_sender():
            return UnifiedEmailSender(
                driver=None,  # 测试时不需要实际驱动
                strategy=SendingStrategy.ULTRA_FAST
            )
        
        email_manager.set_sender_factory(create_test_sender)
        print("   ✅ 发送器工厂设置完成")
        
        # 4. 测试发送器创建
        try:
            test_sender = email_manager.sender_factory()
            print("   ✅ 发送器创建测试成功")
        except Exception as e:
            print(f"   ❌ 发送器创建测试失败: {e}")
            return False
        
        # 5. 检查发送状态
        status = email_manager.get_sending_status()
        print(f"   📊 发送管理器状态: {status['status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成流程测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 邮件发送流程全面验证")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("邮件发送管理器", test_email_sending_manager),
        ("任务队列系统", test_task_queue_system),
        ("统一邮件发送器", test_unified_email_sender),
        ("账号管理", test_account_management),
        ("集成流程", test_integration_flow)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 邮件发送流程完全正常！")
        print("\n💡 现在应该能够:")
        print("   1. 正确创建和管理任务队列")
        print("   2. 智能选择和管理账号")
        print("   3. 稳定创建发送器")
        print("   4. 成功启动557个任务的发送")
    elif passed >= 3:
        print("🎯 大部分功能正常！")
        print("   邮件发送流程基本可用")
    else:
        print("⚠️ 仍有重要问题需要解决")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
