#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器启动测试脚本
测试多浏览器并发启动是否正常
"""

import sys
import os
import time
sys.path.insert(0, '.')

def test_single_browser():
    """测试单个浏览器启动"""
    print("🔧 测试单个浏览器启动...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        chrome_options = Options()
        
        # 基础稳定性配置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-software-rasterizer')
        chrome_options.add_argument('--disable-background-timer-throttling')
        chrome_options.add_argument('--disable-backgrounding-occluded-windows')
        chrome_options.add_argument('--disable-renderer-backgrounding')
        chrome_options.add_argument('--disable-features=TranslateUI')
        chrome_options.add_argument('--disable-ipc-flooding-protection')
        
        # 解决DevToolsActivePort问题
        chrome_options.add_argument('--remote-debugging-port=0')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')
        
        # 反检测配置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 用户数据目录
        user_data_dir = "temp_browser_test"
        os.makedirs(user_data_dir, exist_ok=True)
        chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
        
        # 窗口配置
        chrome_options.add_argument('--headless=new')
        
        # 性能优化
        chrome_options.add_argument('--memory-pressure-off')
        chrome_options.add_argument('--max_old_space_size=4096')
        
        # 日志配置
        chrome_options.add_argument('--log-level=3')
        chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
        
        print("   🚀 正在启动Chrome浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(10)
        
        # 设置反检测
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("   ✅ 浏览器启动成功")
        
        # 测试访问网页
        print("   🌐 测试访问新浪邮箱...")
        driver.get("https://mail.sina.com.cn")
        time.sleep(3)
        
        print(f"   📄 页面标题: {driver.title}")
        print("   ✅ 网页访问成功")
        
        # 关闭浏览器
        driver.quit()
        print("   🔒 浏览器已关闭")
        
        # 清理临时目录
        import shutil
        try:
            shutil.rmtree(user_data_dir)
            print("   🧹 临时目录已清理")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"   ❌ 浏览器启动失败: {e}")
        return False

def test_multiple_browsers():
    """测试多个浏览器同时启动"""
    print("\n🔧 测试多个浏览器同时启动...")
    
    drivers = []
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        for browser_id in range(2):
            print(f"   🚀 正在启动浏览器{browser_id}...")
            
            chrome_options = Options()
            
            # 基础稳定性配置
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--remote-debugging-port=0')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')
            
            # 反检测配置
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 独立用户数据目录
            user_data_dir = f"temp_browser_test_{browser_id}"
            os.makedirs(user_data_dir, exist_ok=True)
            chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
            
            # 窗口配置
            chrome_options.add_argument('--headless=new')
            chrome_options.add_argument('--log-level=3')
            
            # 创建浏览器
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(10)
            
            drivers.append((driver, user_data_dir))
            print(f"   ✅ 浏览器{browser_id}启动成功")
            
            # 短暂延迟避免资源冲突
            time.sleep(2)
        
        print(f"   🎉 成功启动{len(drivers)}个浏览器")
        
        # 测试并发访问
        print("   🌐 测试并发访问网页...")
        for i, (driver, _) in enumerate(drivers):
            try:
                driver.get("https://mail.sina.com.cn")
                print(f"   ✅ 浏览器{i}访问成功")
            except Exception as e:
                print(f"   ⚠️ 浏览器{i}访问失败: {e}")
        
        # 关闭所有浏览器
        print("   🔒 关闭所有浏览器...")
        for i, (driver, user_data_dir) in enumerate(drivers):
            try:
                driver.quit()
                print(f"   ✅ 浏览器{i}已关闭")
                
                # 清理临时目录
                import shutil
                try:
                    shutil.rmtree(user_data_dir)
                except:
                    pass
            except Exception as e:
                print(f"   ⚠️ 关闭浏览器{i}时出错: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 多浏览器测试失败: {e}")
        
        # 清理已创建的浏览器
        for driver, user_data_dir in drivers:
            try:
                driver.quit()
                import shutil
                shutil.rmtree(user_data_dir)
            except:
                pass
        
        return False

def test_concurrent_manager():
    """测试并发发送管理器"""
    print("\n🔧 测试并发发送管理器...")
    
    try:
        from src.core.concurrent_email_sender_manager import ConcurrentEmailSenderManager, ConcurrentSendingConfig
        from src.models.account import Account
        
        # 创建测试配置
        config = ConcurrentSendingConfig(
            max_browsers=2,
            send_interval=2.0,
            account_switch_threshold=5,
            enable_headless=True,
            browser_timeout=30,
            user_data_dir_base="temp_concurrent_test"
        )
        
        # 创建管理器
        manager = ConcurrentEmailSenderManager(config)
        
        print("   ✅ 并发发送管理器创建成功")
        
        # 测试浏览器创建方法
        print("   🔧 测试浏览器创建方法...")
        driver = manager._create_browser_driver(0)
        
        if driver:
            print("   ✅ 浏览器创建方法正常")
            driver.quit()
            
            # 清理临时目录
            import shutil
            try:
                shutil.rmtree("temp_concurrent_test")
            except:
                pass
            
            return True
        else:
            print("   ❌ 浏览器创建方法失败")
            return False
        
    except Exception as e:
        print(f"   ❌ 并发发送管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 浏览器启动测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("单个浏览器启动", test_single_browser),
        ("多个浏览器启动", test_multiple_browsers),
        ("并发发送管理器", test_concurrent_manager)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有浏览器测试通过！多浏览器并发发送应该可以正常工作")
        print("\n💡 建议:")
        print("   1. 重新启动程序测试多浏览器发送功能")
        print("   2. 如果仍有问题，可以尝试关闭无头模式")
        print("   3. 确保系统有足够的内存运行多个浏览器")
    else:
        print("⚠️ 部分测试失败，需要进一步排查")
        print("\n💡 可能的解决方案:")
        print("   1. 更新Chrome浏览器到最新版本")
        print("   2. 更新ChromeDriver到匹配版本")
        print("   3. 检查系统防火墙设置")
        print("   4. 尝试以管理员权限运行程序")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
