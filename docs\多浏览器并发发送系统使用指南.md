# 多浏览器并发发送系统使用指南

## 🎯 系统概述

多浏览器并发发送系统是一个智能化的邮件批量发送解决方案，支持多个浏览器同时工作，每个浏览器可以智能切换多个账号，实现真正的高效并发发送。

## ✨ 核心特性

### 1. 智能模式判断
- **单浏览器模式**：当同时账号数=1时自动启用
- **多浏览器并发模式**：当同时账号数>1时自动启用
- **智能任务分配**：根据收件人总数÷发送模式÷账号数量智能分配

### 2. 真正的并发发送
- 每个浏览器独立线程运行
- 浏览器之间互不干扰
- 支持最多10个浏览器同时工作
- 智能负载均衡

### 3. Cookie账号切换
- 同一浏览器下智能切换账号
- Cookie预加载和无缝切换
- 登录状态自动检测
- 失败自动重试

### 4. 丰富的配置选项
- 发送间隔控制
- 账号切换策略
- 错误处理策略
- 性能模式选择

## 🚀 快速开始

### 步骤1：准备账号
1. 在"账号管理"中添加多个新浪邮箱账号
2. 确保账号状态为"正常"
3. 建议准备3-10个账号以获得最佳并发效果

### 步骤2：配置发送参数
1. 打开"多浏览器发送"标签页
2. 设置基础参数：
   - **同时账号数**：设置为大于1的数值（如3-5）
   - **每账号发送**：每个账号发送多少封后切换（建议10-20）
   - **发送间隔**：邮件之间的间隔时间（建议2-5秒）

### 步骤3：启用智能并发模式
1. 勾选"启用智能并发模式"
2. 系统会自动显示"智能并发Chrome浏览器"状态
3. 配置高级选项（可选）

### 步骤4：添加任务
1. 输入邮件主题和内容
2. 添加收件人邮箱（每行一个）
3. 选择发送模式（单独发送/批量发送）
4. 点击"添加到任务队列"

### 步骤5：启动发送
1. 点击"启动发送器"
2. 系统会自动：
   - 分析发送策略
   - 创建多个浏览器
   - 分配任务到各浏览器
   - 开始并发发送
3. 实时监控发送进度

## ⚙️ 高级配置

### 并发控制
- **最大并发浏览器**：系统支持的最大浏览器数量
- **负载均衡策略**：任务分配策略（轮询、最少任务优先等）

### 账号管理
- **账号切换策略**：
  - 按发送数量切换：发送指定数量后切换
  - 按时间间隔切换：定时切换账号
  - 智能切换：根据发送状态智能判断
  - 手动切换：用户手动控制

### 错误处理
- **失败重试次数**：发送失败时的重试次数
- **发送超时**：单封邮件的最大发送时间
- **错误处理策略**：
  - 立即重试：失败后立即重试
  - 延迟重试：等待一段时间后重试
  - 跳过错误：跳过失败的邮件继续发送
  - 停止发送：遇到错误时停止整个发送过程

### 性能优化
- **性能模式**：
  - 平衡模式：平衡速度和稳定性
  - 高速模式：最大化发送速度
  - 稳定模式：优先保证发送成功率
  - 节能模式：降低系统资源占用

## 📊 监控和统计

### 实时监控
- 发送状态显示
- 成功/失败统计
- 发送速度（邮件/分钟）
- 各浏览器工作状态

### 详细日志
- 发送过程日志
- 错误信息记录
- 账号切换记录
- 性能统计信息

## 🔧 故障排除

### 常见问题

#### 1. 浏览器创建失败
**原因**：Chrome浏览器未正确安装或版本不兼容
**解决**：
- 确保安装了最新版Chrome浏览器
- 检查ChromeDriver版本是否匹配
- 重启程序重试

#### 2. 账号登录失败
**原因**：账号密码错误或账号被限制
**解决**：
- 检查账号密码是否正确
- 确认账号未被冻结
- 手动登录测试账号状态

#### 3. 发送速度慢
**原因**：配置参数不当或网络问题
**解决**：
- 增加同时账号数
- 减少发送间隔时间
- 选择"高速模式"
- 检查网络连接

#### 4. 内存占用过高
**原因**：浏览器数量过多或长时间运行
**解决**：
- 减少最大并发浏览器数
- 选择"节能模式"
- 定期重启程序

### 性能优化建议

#### 1. 账号配置
- 使用3-5个账号获得最佳性能
- 确保账号状态良好
- 定期更新账号Cookie

#### 2. 发送策略
- 单独发送模式适合个性化邮件
- 批量发送模式适合通知类邮件
- 根据邮件类型选择合适模式

#### 3. 系统资源
- 建议8GB以上内存
- 确保足够的磁盘空间
- 关闭不必要的其他程序

## 📈 最佳实践

### 1. 发送前准备
- 测试少量邮件确认配置正确
- 检查邮件内容和格式
- 验证收件人邮箱有效性

### 2. 发送过程中
- 监控发送状态和错误日志
- 适时调整发送参数
- 避免频繁操作界面

### 3. 发送后处理
- 检查发送统计结果
- 处理失败的邮件
- 清理临时文件和日志

## 🔒 安全注意事项

### 1. 账号安全
- 使用专门的发送账号
- 定期更换账号密码
- 避免在多个设备同时登录

### 2. 发送频率
- 遵守邮件服务商的发送限制
- 避免短时间内大量发送
- 合理设置发送间隔

### 3. 内容合规
- 确保邮件内容合法合规
- 避免发送垃圾邮件
- 尊重收件人的退订请求

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 查看详细错误日志
2. 检查系统配置是否正确
3. 参考本文档的故障排除部分
4. 联系技术支持获取帮助

---

**版本**：v1.0  
**更新日期**：2025-08-05  
**适用系统**：Windows 10/11
