#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化窗口模式修复测试脚本
专门测试最小化窗口模式的网络连接问题修复
"""

import sys
import os
import time
sys.path.insert(0, '.')

def test_minimized_window_mode():
    """测试最小化窗口模式"""
    print("📱 测试最小化窗口模式修复...")
    
    try:
        from src.core.browser_manager import BrowserManager
        from src.models.account import AccountManager
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        # 获取可用账号
        account_manager = AccountManager(db_manager)
        available_accounts = account_manager.get_available_accounts()
        
        if not available_accounts:
            print("   ❌ 没有可用账号")
            return False, None
        
        test_account = available_accounts[0]
        print(f"   🧪 测试账号: {test_account.email}")
        
        # 创建浏览器管理器 - 使用最小化窗口模式
        config_dict = getattr(config, 'config', {})
        browser_config = config_dict.get('browser', {})
        browser_config['window_size'] = [1280, 720]
        browser_config['window_mode'] = '最小化窗口'  # 关键：使用最小化窗口模式
        config_dict['browser'] = browser_config
        
        browser_manager = BrowserManager(config_dict)
        print("   ✅ 浏览器管理器创建成功（最小化窗口模式）")
        
        # 创建浏览器驱动
        print(f"   🚀 创建浏览器驱动（最小化窗口模式）...")
        driver_id = browser_manager.create_driver(test_account)
        
        if driver_id:
            print(f"   ✅ 浏览器驱动创建成功: {driver_id}")
            
            # 获取驱动并检查状态
            driver = browser_manager.get_driver(driver_id)
            if driver:
                print("   ✅ 浏览器驱动获取成功")
                
                # 等待页面加载
                time.sleep(5)
                
                # 检查驱动是否存活
                try:
                    current_url = driver.current_url
                    page_title = driver.title
                    print(f"   📊 当前URL: {current_url}")
                    print(f"   📊 页面标题: {page_title}")
                    
                    # 测试驱动存活性
                    print("   🔍 测试驱动存活性...")
                    try:
                        test_url = driver.current_url
                        print("   ✅ 驱动存活性测试通过")
                        driver_alive = True
                    except Exception as e:
                        print(f"   ❌ 驱动存活性测试失败: {e}")
                        driver_alive = False
                    
                    # 测试基本导航
                    if driver_alive:
                        print("   🔍 测试基本导航功能...")
                        try:
                            driver.get("about:blank")
                            time.sleep(2)
                            blank_url = driver.current_url
                            print(f"   ✅ 导航测试成功: {blank_url}")
                            navigation_ok = True
                        except Exception as e:
                            print(f"   ❌ 导航测试失败: {e}")
                            navigation_ok = False
                    else:
                        navigation_ok = False
                    
                    return driver_alive and navigation_ok, driver
                    
                except Exception as e:
                    print(f"   ❌ 检查浏览器状态失败: {e}")
                    return False, None
            else:
                print("   ❌ 浏览器驱动获取失败")
                return False, None
        else:
            print("   ❌ 浏览器驱动创建失败")
            return False, None
            
    except Exception as e:
        print(f"   ❌ 最小化窗口模式测试失败: {e}")
        return False, None

def test_normal_window_mode():
    """测试正常窗口模式（对比）"""
    print("\n🖥️ 测试正常窗口模式（对比）...")
    
    try:
        from src.core.browser_manager import BrowserManager
        from src.models.account import AccountManager
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        # 获取可用账号
        account_manager = AccountManager(db_manager)
        available_accounts = account_manager.get_available_accounts()
        
        if not available_accounts:
            print("   ❌ 没有可用账号")
            return False, None
        
        test_account = available_accounts[0]
        print(f"   🧪 测试账号: {test_account.email}")
        
        # 创建浏览器管理器 - 使用正常窗口模式
        config_dict = getattr(config, 'config', {})
        browser_config = config_dict.get('browser', {})
        browser_config['window_size'] = [1280, 720]
        browser_config['window_mode'] = '正常窗口'  # 使用正常窗口模式
        config_dict['browser'] = browser_config
        
        browser_manager = BrowserManager(config_dict)
        print("   ✅ 浏览器管理器创建成功（正常窗口模式）")
        
        # 创建浏览器驱动
        print(f"   🚀 创建浏览器驱动（正常窗口模式）...")
        driver_id = browser_manager.create_driver(test_account)
        
        if driver_id:
            print(f"   ✅ 浏览器驱动创建成功: {driver_id}")
            
            # 获取驱动并检查状态
            driver = browser_manager.get_driver(driver_id)
            if driver:
                print("   ✅ 浏览器驱动获取成功")
                
                # 等待页面加载
                time.sleep(3)
                
                # 检查驱动是否存活
                try:
                    current_url = driver.current_url
                    page_title = driver.title
                    print(f"   📊 当前URL: {current_url}")
                    print(f"   📊 页面标题: {page_title}")
                    
                    return True, driver
                    
                except Exception as e:
                    print(f"   ❌ 检查浏览器状态失败: {e}")
                    return False, None
            else:
                print("   ❌ 浏览器驱动获取失败")
                return False, None
        else:
            print("   ❌ 浏览器驱动创建失败")
            return False, None
            
    except Exception as e:
        print(f"   ❌ 正常窗口模式测试失败: {e}")
        return False, None

def test_multi_browser_widget_fixed():
    """测试修复后的多浏览器组件"""
    print("\n🔧 测试修复后的多浏览器组件...")
    
    try:
        from src.gui.optimized_multi_browser_widget import OptimizedMultiBrowserWidget
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        from PyQt5.QtWidgets import QApplication
        import sys
        
        # 创建QApplication（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        # 创建多浏览器组件
        browser_widget = OptimizedMultiBrowserWidget(db_manager)
        print("   ✅ 多浏览器组件创建成功")
        
        # 设置浏览器数量为1
        browser_widget.browser_count_spin.setValue(1)
        
        # 测试浏览器创建
        print("   🚀 测试浏览器创建（使用修复后的最小化模式）...")
        browser_widget._create_browser_drivers()
        
        # 检查创建结果
        if hasattr(browser_widget, 'browser_drivers') and browser_widget.browser_drivers:
            print(f"   ✅ 成功创建 {len(browser_widget.browser_drivers)} 个浏览器")
            
            # 测试驱动验证
            print("   🔍 测试驱动验证...")
            validation_result = browser_widget._validate_browser_drivers()
            
            if validation_result:
                print("   ✅ 驱动验证通过")
                return True
            else:
                print("   ❌ 驱动验证失败")
                return False
        else:
            print("   ❌ 没有创建任何浏览器")
            return False
            
    except Exception as e:
        print(f"   ❌ 多浏览器组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 最小化窗口模式修复测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("正常窗口模式", test_normal_window_mode),
        ("最小化窗口模式", test_minimized_window_mode),
        ("多浏览器组件", test_multi_browser_widget_fixed)
    ]
    
    results = []
    drivers = []
    
    for test_name, test_func in tests:
        try:
            if test_name in ["正常窗口模式", "最小化窗口模式"]:
                result, driver = test_func()
                results.append((test_name, result))
                if driver:
                    drivers.append(driver)
            else:
                result = test_func()
                results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 等待观察
    if drivers:
        print("\n⏳ 等待10秒供观察...")
        time.sleep(10)
        
        # 清理浏览器
        for driver in drivers:
            try:
                driver.quit()
                print("✅ 浏览器清理成功")
            except:
                pass
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 最小化窗口模式修复完全成功！")
        print("\n💡 修复效果:")
        print("   ✅ 最小化窗口模式网络连接正常")
        print("   ✅ 与正常窗口模式功能一致")
        print("   ✅ 多浏览器组件可以正常工作")
        print("\n🚀 系统现在可以在最小化模式下正常启动发送功能！")
    elif passed >= 2:
        print("🎯 大部分功能正常！")
        print("   最小化窗口模式基本修复成功")
    else:
        print("⚠️ 仍有问题需要进一步调试")
    
    return passed >= 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
