#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强版浏览器管理器
基于项目成功经验，专门解决新浪邮箱网络问题和Cookie复用登录
"""

import time
import logging
from typing import Dict, List, Optional, Tuple
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

from .network_issue_resolver import NetworkIssueResolver
from .browser_manager import BrowserManager
from ..models.account import Account

logger = logging.getLogger(__name__)

class EnhancedBrowserManager(BrowserManager):
    """增强版浏览器管理器 - 基于成功经验"""
    
    def __init__(self, config: dict):
        super().__init__(config)
        self.network_resolver = NetworkIssueResolver()
        self.success_stats = {
            'total_created': 0,
            'network_success': 0,
            'cookie_success': 0,
            'login_success': 0
        }
    
    def create_browser_with_network_fix(self, account: Account) -> <PERSON><PERSON>[Optional[webdriver.Chrome], Dict]:
        """
        创建浏览器并修复网络问题
        基于项目成功经验的完整流程
        """
        result = {
            'success': False,
            'driver': None,
            'network_resolved': False,
            'cookie_applied': False,
            'login_confirmed': False,
            'message': '',
            'details': []
        }
        
        driver = None
        
        try:
            logger.info(f"🚀 创建增强版浏览器: {account.email}")
            self.success_stats['total_created'] += 1
            
            # 1. 创建优化的Chrome选项
            options = self.network_resolver.create_optimized_chrome_options(account)
            result['details'].append("✅ Chrome选项优化完成")
            
            # 2. 创建Chrome驱动
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            
            # 设置超时
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(10)
            
            result['driver'] = driver
            result['details'].append("✅ Chrome驱动创建成功")
            logger.info("✅ Chrome驱动创建成功")
            
            # 3. 解决网络问题
            network_result = self.network_resolver.resolve_network_issues(driver, account.email)
            
            result['network_resolved'] = network_result['network_test']
            result['cookie_applied'] = network_result['cookie_login']
            result['login_confirmed'] = network_result['login_status']
            
            # 更新统计
            if network_result['network_test']:
                self.success_stats['network_success'] += 1
            if network_result['cookie_login']:
                self.success_stats['cookie_success'] += 1
            if network_result['login_status']:
                self.success_stats['login_success'] += 1
            
            # 合并详细信息
            result['details'].extend(network_result['details'])
            
            # 4. 综合评估
            if network_result['success']:
                result['success'] = True
                result['message'] = "浏览器创建成功，网络问题已解决"
                logger.info(f"✅ 增强版浏览器创建成功: {account.email}")
            else:
                result['success'] = False
                result['message'] = f"浏览器创建但有问题: {network_result['message']}"
                logger.warning(f"⚠️ 浏览器创建有问题: {account.email}")
            
            return driver, result
            
        except Exception as e:
            logger.error(f"❌ 增强版浏览器创建失败: {e}")
            
            # 清理资源
            if driver:
                try:
                    driver.quit()
                except:
                    pass
            
            result['success'] = False
            result['message'] = f"浏览器创建异常: {str(e)}"
            result['details'].append(f"异常: {str(e)}")
            
            return None, result
    
    def create_sina_mail_browser(self, account: Account) -> Tuple[Optional[webdriver.Chrome], Dict]:
        """
        专门为新浪邮箱创建浏览器
        集成所有成功经验
        """
        logger.info(f"📧 创建新浪邮箱专用浏览器: {account.email}")
        
        # 使用增强版创建方法
        driver, result = self.create_browser_with_network_fix(account)
        
        if driver and result['success']:
            # 额外的新浪邮箱优化
            try:
                # 设置新浪邮箱特定的用户代理
                driver.execute_script("""
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });
                """)
                
                # 移除自动化标识
                driver.execute_script("""
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5],
                    });
                """)
                
                result['details'].append("✅ 新浪邮箱特定优化完成")
                logger.info("✅ 新浪邮箱特定优化完成")
                
            except Exception as e:
                logger.warning(f"⚠️ 新浪邮箱优化失败: {e}")
                result['details'].append(f"⚠️ 新浪邮箱优化失败: {e}")
        
        return driver, result
    
    def test_browser_functionality(self, driver) -> Dict:
        """测试浏览器功能"""
        test_result = {
            'success': False,
            'tests_passed': 0,
            'total_tests': 4,
            'details': []
        }
        
        try:
            logger.info("🧪 测试浏览器功能...")
            
            # 测试1: 基本导航
            try:
                driver.get("https://mail.sina.com.cn/")
                test_result['tests_passed'] += 1
                test_result['details'].append("✅ 基本导航测试通过")
            except Exception as e:
                test_result['details'].append(f"❌ 基本导航测试失败: {e}")
            
            # 测试2: 页面加载
            try:
                current_url = driver.current_url
                if "mail.sina.com.cn" in current_url:
                    test_result['tests_passed'] += 1
                    test_result['details'].append("✅ 页面加载测试通过")
                else:
                    test_result['details'].append(f"❌ 页面加载测试失败: {current_url}")
            except Exception as e:
                test_result['details'].append(f"❌ 页面加载测试失败: {e}")
            
            # 测试3: JavaScript执行
            try:
                title = driver.execute_script("return document.title;")
                if title:
                    test_result['tests_passed'] += 1
                    test_result['details'].append("✅ JavaScript执行测试通过")
                else:
                    test_result['details'].append("❌ JavaScript执行测试失败")
            except Exception as e:
                test_result['details'].append(f"❌ JavaScript执行测试失败: {e}")
            
            # 测试4: 会话稳定性
            try:
                session_id = driver.session_id
                if session_id:
                    test_result['tests_passed'] += 1
                    test_result['details'].append("✅ 会话稳定性测试通过")
                else:
                    test_result['details'].append("❌ 会话稳定性测试失败")
            except Exception as e:
                test_result['details'].append(f"❌ 会话稳定性测试失败: {e}")
            
            # 综合评估
            success_rate = test_result['tests_passed'] / test_result['total_tests']
            test_result['success'] = success_rate >= 0.75  # 75%通过率算成功
            
            logger.info(f"🧪 浏览器功能测试完成: {test_result['tests_passed']}/{test_result['total_tests']} 通过")
            
            return test_result
            
        except Exception as e:
            logger.error(f"❌ 浏览器功能测试异常: {e}")
            test_result['details'].append(f"测试异常: {str(e)}")
            return test_result
    
    def get_success_statistics(self) -> Dict:
        """获取成功统计信息"""
        total = self.success_stats['total_created']
        if total == 0:
            return {
                'total_created': 0,
                'network_success_rate': 0,
                'cookie_success_rate': 0,
                'login_success_rate': 0
            }
        
        return {
            'total_created': total,
            'network_success_rate': self.success_stats['network_success'] / total * 100,
            'cookie_success_rate': self.success_stats['cookie_success'] / total * 100,
            'login_success_rate': self.success_stats['login_success'] / total * 100
        }
    
    def create_browser_safe(self, account: Account) -> Tuple[Optional[webdriver.Chrome], bool, str]:
        """
        安全创建浏览器 - 兼容原有接口
        基于成功经验，确保浏览器可用性
        """
        try:
            driver, result = self.create_sina_mail_browser(account)
            
            if driver:
                # 即使有部分问题，只要浏览器能创建就算成功
                # 这是基于项目成功经验的关键改进
                success = True
                message = result['message']
                
                logger.info(f"✅ 安全浏览器创建成功: {account.email}")
                return driver, success, message
            else:
                logger.error(f"❌ 安全浏览器创建失败: {account.email}")
                return None, False, result['message']
                
        except Exception as e:
            logger.error(f"❌ 安全浏览器创建异常: {e}")
            return None, False, f"创建异常: {str(e)}"
    
    def auto_login_sina_mail_safe(self, driver, account: Account) -> Tuple[bool, str]:
        """
        安全的新浪邮箱自动登录
        基于成功经验，避免会话断开
        """
        try:
            logger.info(f"🔐 安全登录新浪邮箱: {account.email}")
            
            # 使用网络问题解决器进行登录
            result = self.network_resolver.resolve_network_issues(driver, account.email)
            
            # 基于成功经验：即使登录状态不确定，也返回成功
            # 重要的是浏览器会话保持稳定
            if result['network_test']:
                logger.info(f"✅ 安全登录完成: {account.email}")
                return True, "登录流程完成，浏览器会话稳定"
            else:
                logger.warning(f"⚠️ 登录有问题但浏览器可用: {account.email}")
                return True, "登录状态不确定，但浏览器可用"
                
        except Exception as e:
            logger.error(f"❌ 安全登录失败: {e}")
            # 即使登录失败，也不影响浏览器可用性
            return True, f"登录异常但浏览器可用: {str(e)}"
