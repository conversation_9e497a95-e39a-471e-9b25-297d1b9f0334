#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重试机制验证测试脚本
测试多浏览器组件的重试机制修复效果
"""

import sys
import os
import time
sys.path.insert(0, '.')

def test_multi_browser_with_retry():
    """测试带重试机制的多浏览器组件"""
    print("🔄 测试带重试机制的多浏览器组件...")
    
    try:
        from src.gui.optimized_multi_browser_widget import OptimizedMultiBrowserWidget
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        from PyQt5.QtWidgets import QApplication
        import sys
        
        # 创建QApplication（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        # 创建多浏览器组件
        browser_widget = OptimizedMultiBrowserWidget(db_manager)
        print("   ✅ 多浏览器组件创建成功")
        
        # 设置浏览器数量为1（减少测试复杂度）
        browser_widget.browser_count_spin.setValue(1)
        
        # 确保使用最小化窗口模式
        browser_widget.window_mode_combo.setCurrentText("最小化窗口")
        print("   ✅ 设置为最小化窗口模式")
        
        # 测试浏览器创建（带重试机制）
        print("   🚀 测试浏览器创建（带重试机制）...")
        browser_widget._create_browser_drivers()
        
        # 检查创建结果
        if hasattr(browser_widget, 'browser_drivers') and browser_widget.browser_drivers:
            print(f"   ✅ 成功创建 {len(browser_widget.browser_drivers)} 个浏览器")
            
            # 测试驱动验证
            print("   🔍 测试驱动验证...")
            validation_result = browser_widget._validate_browser_drivers()
            
            if validation_result:
                print("   ✅ 驱动验证通过")
                
                # 测试发送器工厂设置
                print("   🔧 测试发送器工厂设置...")
                try:
                    setup_result = browser_widget._setup_sender_factory()
                    if setup_result:
                        print("   ✅ 发送器工厂设置成功")
                        return True
                    else:
                        print("   ❌ 发送器工厂设置失败")
                        return False
                except Exception as e:
                    print(f"   ❌ 发送器工厂设置异常: {e}")
                    return False
            else:
                print("   ❌ 驱动验证失败")
                return False
        else:
            print("   ❌ 没有创建任何浏览器")
            return False
            
    except Exception as e:
        print(f"   ❌ 多浏览器组件测试失败: {e}")
        return False

def test_startup_simulation():
    """模拟完整的启动流程"""
    print("\n🚀 模拟完整的启动流程...")
    
    try:
        from src.gui.optimized_multi_browser_widget import OptimizedMultiBrowserWidget
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        from PyQt5.QtWidgets import QApplication
        import sys
        
        # 创建QApplication（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        # 创建多浏览器组件
        browser_widget = OptimizedMultiBrowserWidget(db_manager)
        print("   ✅ 多浏览器组件创建成功")
        
        # 设置参数
        browser_widget.browser_count_spin.setValue(1)
        browser_widget.window_mode_combo.setCurrentText("最小化窗口")
        
        # 模拟启动发送器
        print("   🚀 模拟启动发送器...")
        try:
            # 这里模拟 _start_concurrent_sending 的关键步骤
            
            # 1. 创建浏览器驱动
            print("   1. 创建浏览器驱动...")
            browser_widget._create_browser_drivers()
            
            if not (hasattr(browser_widget, 'browser_drivers') and browser_widget.browser_drivers):
                print("   ❌ 浏览器创建失败")
                return False
            
            print(f"   ✅ 成功创建 {len(browser_widget.browser_drivers)} 个浏览器")
            
            # 2. 验证浏览器驱动
            print("   2. 验证浏览器驱动...")
            validation_result = browser_widget._validate_browser_drivers()
            
            if not validation_result:
                print("   ❌ 浏览器验证失败")
                return False
            
            print("   ✅ 浏览器验证成功")
            
            # 3. 设置发送器工厂
            print("   3. 设置发送器工厂...")
            setup_result = browser_widget._setup_sender_factory()
            
            if not setup_result:
                print("   ❌ 发送器工厂设置失败")
                return False
            
            print("   ✅ 发送器工厂设置成功")
            
            # 4. 模拟启动邮件发送管理器
            print("   4. 模拟启动邮件发送管理器...")
            # 这里只是模拟，不实际启动
            print("   ✅ 邮件发送管理器准备就绪")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 启动流程异常: {e}")
            return False
            
    except Exception as e:
        print(f"   ❌ 启动流程测试失败: {e}")
        return False

def test_network_stability():
    """测试网络稳定性"""
    print("\n🌐 测试网络稳定性...")
    
    try:
        # 连续创建多个浏览器测试网络稳定性
        from src.core.browser_manager import BrowserManager
        from src.models.account import AccountManager
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        # 获取可用账号
        account_manager = AccountManager(db_manager)
        available_accounts = account_manager.get_available_accounts()
        
        if not available_accounts:
            print("   ❌ 没有可用账号")
            return False
        
        test_account = available_accounts[0]
        
        # 创建浏览器管理器
        config_dict = getattr(config, 'config', {})
        browser_config = config_dict.get('browser', {})
        browser_config['window_size'] = [1280, 720]
        browser_config['window_mode'] = '最小化窗口'
        config_dict['browser'] = browser_config
        
        browser_manager = BrowserManager(config_dict)
        
        # 连续创建3个浏览器测试稳定性
        success_count = 0
        total_tests = 3
        
        for i in range(total_tests):
            try:
                print(f"   🔍 测试 {i+1}/{total_tests}...")
                driver_id = browser_manager.create_driver(test_account)
                
                if driver_id:
                    driver = browser_manager.get_driver(driver_id)
                    if driver:
                        # 简单测试
                        current_url = driver.current_url
                        print(f"      ✅ 成功: {current_url}")
                        success_count += 1
                        
                        # 清理
                        driver.quit()
                    else:
                        print(f"      ❌ 驱动获取失败")
                else:
                    print(f"      ❌ 驱动创建失败")
                
                # 间隔时间
                if i < total_tests - 1:
                    time.sleep(3)
                    
            except Exception as e:
                print(f"      ❌ 测试异常: {e}")
        
        print(f"   📊 网络稳定性测试结果: {success_count}/{total_tests}")
        return success_count >= 2  # 至少2/3成功
        
    except Exception as e:
        print(f"   ❌ 网络稳定性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 重试机制验证测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("网络稳定性测试", test_network_stability),
        ("多浏览器重试机制", test_multi_browser_with_retry),
        ("完整启动流程模拟", test_startup_simulation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 重试机制修复完全成功！")
        print("\n💡 修复效果:")
        print("   ✅ 网络连接稳定性提升")
        print("   ✅ 多浏览器组件重试机制正常")
        print("   ✅ 完整启动流程可以正常工作")
        print("\n🚀 系统现在可以稳定启动发送功能！")
        print("💡 建议操作:")
        print("   1. 重新启动主程序: python main.py")
        print("   2. 创建邮件发送任务")
        print("   3. 启动发送功能")
        print("   4. 发送测试邮件到 <EMAIL>")
    elif passed >= 2:
        print("🎯 大部分功能正常！")
        print("   重试机制基本生效，系统稳定性提升")
    else:
        print("⚠️ 仍有问题需要进一步调试")
        print("   建议检查网络环境或联系技术支持")
    
    return passed >= 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
