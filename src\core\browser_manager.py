#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器管理模块
负责浏览器的创建、配置和管理，支持代理设置和多浏览器实例
"""

import os
import time
import random
from pathlib import Path
from typing import Optional, Dict, Any, List
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from src.utils.logger import get_logger
from src.models.account import Account

logger = get_logger("BrowserManager")


class BrowserManager:
    """浏览器管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化浏览器管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.drivers: Dict[str, webdriver.Chrome] = {}
        self.driver_count = 0
        self.max_drivers = config.get('performance.max_concurrent_browsers', 3)
        
        # 浏览器配置
        self.browser_config = config.get('browser', {})
        self.sina_config = config.get('sina_email', {})
        
        logger.info("浏览器管理器初始化完成")
    
    def create_driver(self, account: Account, driver_id: Optional[str] = None) -> str:
        """
        创建浏览器驱动实例
        
        Args:
            account: 账号信息（包含代理配置）
            driver_id: 驱动ID，如果为None则自动生成
        
        Returns:
            驱动ID
        """
        if self.driver_count >= self.max_drivers:
            raise Exception(f"已达到最大浏览器数量限制: {self.max_drivers}")
        
        if driver_id is None:
            driver_id = f"driver_{int(time.time())}_{random.randint(1000, 9999)}"
        
        try:
            # 创建Chrome选项
            options = self._create_chrome_options(account)
            
            # 创建服务 - 优化启动速度
            service = Service(ChromeDriverManager().install())
            service.start()  # 预启动服务

            # 创建驱动 - 极速模式
            start_time = time.time()
            driver = webdriver.Chrome(service=service, options=options)
            create_time = time.time() - start_time
            logger.info(f"⚡ 浏览器创建耗时: {create_time:.2f}秒")

            # 设置超时 - 优化为快速响应
            driver.implicitly_wait(self.browser_config.get('implicit_wait', 3))  # 减少等待时间
            driver.set_page_load_timeout(self.browser_config.get('page_load_timeout', 20))  # 减少页面加载超时
            driver.set_script_timeout(10)  # 减少脚本执行超时

            # 设置窗口大小
            window_size = self.browser_config.get('window_size', [1920, 1080])
            try:
                driver.set_window_size(window_size[0], window_size[1])
                logger.info(f"窗口大小设置为: {window_size[0]}x{window_size[1]}")
            except Exception as e:
                logger.warning(f"设置窗口大小失败: {e}")

            # 添加错误恢复机制
            try:
                # 测试浏览器连接
                driver.get("about:blank")
                logger.info("浏览器连接测试成功")
            except Exception as e:
                logger.error(f"浏览器连接测试失败: {e}")
                raise

            # 🔑 关键修复：自动登录到新浪邮箱（优化版）
            try:
                login_success = self._auto_login_sina_mail_safe(driver, account)
                if login_success:
                    logger.info(f"🔑 账号自动登录成功: {account.email}")
                else:
                    logger.warning(f"⚠️ 账号自动登录失败，但浏览器仍可用: {account.email}")
            except Exception as e:
                logger.error(f"❌ 自动登录过程异常，但浏览器仍可用: {e}")
                # 登录失败不影响浏览器创建，继续执行

            # 存储驱动
            self.drivers[driver_id] = driver
            self.driver_count += 1

            logger.info(f"浏览器驱动创建成功: {driver_id}")
            return driver_id
            
        except Exception as e:
            logger.error(f"创建浏览器驱动失败: {e}")
            raise

    def _auto_login_sina_mail(self, driver, account: Account) -> bool:
        """自动登录到新浪邮箱 - 优化版本

        Args:
            driver: WebDriver实例
            account: 账号信息

        Returns:
            是否登录成功
        """
        try:
            logger.info(f"🔑 开始自动登录新浪邮箱: {account.email}")

            # 1. 导航到新浪邮箱
            logger.info("📧 导航到新浪邮箱...")
            driver.get("https://mail.sina.com.cn/")

            # 等待页面加载
            import time
            time.sleep(3)

            # 2. 尝试Cookie登录
            cookie_result = self._try_cookie_login_enhanced(driver, account)

            if cookie_result['success']:
                logger.info(f"🍪 Cookie登录成功: {account.email}")
                # 更新账号状态为正常
                self._update_account_status(account, 'active', 'Cookie登录成功')
                return True
            else:
                # Cookie登录失败，更新账号状态并记录原因
                failure_reason = cookie_result.get('reason', 'Cookie登录失败')
                logger.warning(f"⚠️ Cookie登录失败: {account.email} - {failure_reason}")

                # 根据失败原因更新账号状态
                if 'cookie_expired' in failure_reason.lower():
                    self._update_account_status(account, 'cookie_expired', failure_reason)
                elif 'cookie_invalid' in failure_reason.lower():
                    self._update_account_status(account, 'cookie_invalid', failure_reason)
                elif 'account_banned' in failure_reason.lower():
                    self._update_account_status(account, 'banned', failure_reason)
                else:
                    self._update_account_status(account, 'login_failed', failure_reason)

                return False

        except Exception as e:
            logger.error(f"❌ 自动登录异常: {e}")
            self._update_account_status(account, 'error', f'登录异常: {str(e)}')
            return False

    def _auto_login_sina_mail_safe(self, driver, account: Account) -> bool:
        """安全的自动登录到新浪邮箱 - 避免会话断开"""
        try:
            logger.info(f"🔑 开始安全登录新浪邮箱: {account.email}")

            # 1. 导航到新浪邮箱
            logger.info("📧 导航到新浪邮箱...")
            driver.get("https://mail.sina.com.cn/")

            # 等待页面加载
            import time
            time.sleep(3)

            # 2. 检查当前页面状态
            try:
                current_url = driver.current_url
                page_title = driver.title
                logger.info(f"🔍 当前页面 - URL: {current_url}")
                logger.info(f"🔍 当前页面 - 标题: {page_title}")

                # 如果已经在邮箱主页，可能已经登录
                if "mail.sina.com.cn" in current_url and "login" not in current_url.lower():
                    logger.info(f"✅ 已在邮箱主页，可能已登录: {account.email}")
                    self._update_account_status(account, 'active', '已在邮箱主页')
                    return True

            except Exception as e:
                logger.error(f"❌ 检查页面状态失败: {e}")
                return False

            # 3. 尝试Cookie登录（不刷新页面）
            cookie_result = self._try_cookie_login_safe(driver, account)

            if cookie_result['success']:
                logger.info(f"🍪 Cookie登录成功: {account.email}")
                self._update_account_status(account, 'active', 'Cookie登录成功')
                return True
            else:
                # Cookie登录失败，但不影响浏览器可用性
                failure_reason = cookie_result.get('reason', 'Cookie登录失败')
                logger.warning(f"⚠️ Cookie登录失败，但浏览器仍可用: {account.email} - {failure_reason}")

                # 更新账号状态但不标记为错误
                if 'cookie_expired' in failure_reason.lower():
                    self._update_account_status(account, 'cookie_expired', failure_reason)
                elif 'cookie_invalid' in failure_reason.lower():
                    self._update_account_status(account, 'cookie_invalid', failure_reason)
                else:
                    self._update_account_status(account, 'login_failed', failure_reason)

                # 即使Cookie登录失败，浏览器仍然可用
                return True

        except Exception as e:
            logger.error(f"❌ 安全登录异常: {e}")
            # 即使登录异常，浏览器仍然可用
            return True

    def _try_cookie_login_safe(self, driver, account: Account) -> dict:
        """安全的Cookie登录 - 不刷新页面"""
        result = {'success': False, 'reason': ''}

        try:
            # 1. 使用现有Cookie管理器获取Cookie
            cookie_data = self._load_cookies_from_existing_system(account)
            if not cookie_data['success']:
                result['reason'] = cookie_data['reason']
                return result

            cookies = cookie_data['cookies']
            logger.info(f"🍪 从现有系统找到 {len(cookies)} 个Cookie: {account.email}")

            # 2. 清除当前Cookie
            driver.delete_all_cookies()

            # 3. 应用保存的Cookie
            applied_count = 0
            for cookie in cookies:
                try:
                    # 验证Cookie格式
                    if self._validate_cookie(cookie):
                        driver.add_cookie(cookie)
                        applied_count += 1
                    else:
                        logger.debug(f"跳过无效Cookie: {cookie.get('name', 'unknown')}")
                except Exception as e:
                    logger.debug(f"设置Cookie失败: {e}")

            logger.info(f"✅ 成功应用 {applied_count}/{len(cookies)} 个Cookie")

            # 4. 等待Cookie生效（不刷新页面）
            import time
            time.sleep(2)

            # 5. 验证登录状态（简化版，避免复杂操作）
            login_status = self._verify_login_status_simple(driver, account)

            if login_status['success']:
                logger.info(f"✅ Cookie登录验证成功: {account.email}")
                result['success'] = True
                result['reason'] = 'Cookie登录成功'
                return result
            else:
                logger.warning(f"❌ Cookie登录验证失败: {account.email} - {login_status['reason']}")
                result['reason'] = f"cookie_invalid: {login_status['reason']}"
                return result

        except Exception as e:
            logger.error(f"安全Cookie登录异常: {e}")
            result['reason'] = f'Cookie登录异常: {str(e)}'
            return result

    def _verify_login_status_simple(self, driver, account: Account) -> dict:
        """简化的登录状态验证 - 避免复杂操作"""
        result = {'success': False, 'reason': ''}

        try:
            # 检查浏览器会话是否有效
            try:
                current_url = driver.current_url
                page_title = driver.title
            except Exception as session_error:
                result['reason'] = f'session_invalid: {str(session_error)}'
                logger.error(f"❌ 浏览器会话无效: {session_error}")
                return result

            logger.info(f"🔍 简化验证 - URL: {current_url}")
            logger.info(f"🔍 简化验证 - 标题: {page_title}")

            # 简单检查：如果在新浪邮箱域名下且不在登录页面，认为可能已登录
            if "mail.sina.com.cn" in current_url:
                if "login" not in current_url.lower():
                    result['success'] = True
                    result['reason'] = 'on_sina_mail_domain'
                    logger.info(f"✅ 在新浪邮箱域名下，认为已登录: {account.email}")
                    return result
                else:
                    result['reason'] = 'still_on_login_page'
                    logger.warning(f"⚠️ 仍在登录页面: {account.email}")
                    return result
            else:
                result['reason'] = 'not_on_sina_domain'
                logger.warning(f"⚠️ 不在新浪邮箱域名: {account.email}")
                return result

        except Exception as e:
            result['reason'] = f'verification_exception: {str(e)}'
            logger.error(f"简化验证异常: {e}")
            return result

    def _try_cookie_login_enhanced(self, driver, account: Account) -> dict:
        """增强版Cookie登录 - 使用现有Cookie管理器"""
        result = {'success': False, 'reason': ''}

        try:
            # 1. 使用现有Cookie管理器获取Cookie
            cookie_data = self._load_cookies_from_existing_system(account)
            if not cookie_data['success']:
                result['reason'] = cookie_data['reason']
                return result

            cookies = cookie_data['cookies']
            logger.info(f"🍪 从现有系统找到 {len(cookies)} 个Cookie: {account.email}")

            # 2. 清除当前Cookie
            driver.delete_all_cookies()

            # 3. 应用保存的Cookie
            applied_count = 0
            for cookie in cookies:
                try:
                    # 验证Cookie格式
                    if self._validate_cookie(cookie):
                        driver.add_cookie(cookie)
                        applied_count += 1
                    else:
                        logger.debug(f"跳过无效Cookie: {cookie.get('name', 'unknown')}")
                except Exception as e:
                    logger.debug(f"设置Cookie失败: {e}")

            logger.info(f"✅ 成功应用 {applied_count}/{len(cookies)} 个Cookie")

            # 4. 验证登录状态（不刷新页面，避免会话断开）
            import time
            time.sleep(2)  # 等待Cookie生效

            # 5. 验证登录状态
            login_status = self._verify_login_status_enhanced(driver, account)

            if login_status['success']:
                logger.info(f"✅ Cookie登录验证成功: {account.email}")
                result['success'] = True
                result['reason'] = 'Cookie登录成功'
                return result
            else:
                logger.warning(f"❌ Cookie登录验证失败: {account.email} - {login_status['reason']}")
                result['reason'] = f"cookie_invalid: {login_status['reason']}"
                return result

        except Exception as e:
            logger.error(f"Cookie登录异常: {e}")
            result['reason'] = f'Cookie登录异常: {str(e)}'
            return result

    def _load_cookies_from_existing_system(self, account: Account) -> dict:
        """从现有Cookie管理器加载Cookie"""
        result = {'success': False, 'reason': '', 'cookies': []}

        try:
            # 使用现有的Cookie管理器
            from src.core.cookie_manager import CookieManager

            # 创建Cookie管理器实例
            config = getattr(self, 'config', {})
            cookie_manager = CookieManager(config)

            # 获取账号的Cookie
            cookie_data = cookie_manager.get_cookies(account.email)

            if cookie_data and 'cookies' in cookie_data:
                cookies = cookie_data['cookies']

                if cookies and len(cookies) > 0:
                    # 过滤有效的Cookie
                    valid_cookies = []
                    for cookie in cookies:
                        if self._validate_cookie(cookie):
                            valid_cookies.append(cookie)

                    if valid_cookies:
                        result['success'] = True
                        result['cookies'] = valid_cookies
                        result['reason'] = f'loaded_{len(valid_cookies)}_cookies_from_existing_system'
                        logger.info(f"✅ 从现有系统加载 {len(valid_cookies)} 个有效Cookie: {account.email}")
                        return result
                    else:
                        result['reason'] = 'no_valid_cookies_in_existing_system'
                        logger.warning(f"⚠️ 现有系统中没有有效Cookie: {account.email}")
                        return result
                else:
                    result['reason'] = 'empty_cookies_in_existing_system'
                    logger.info(f"📝 现有系统中Cookie列表为空: {account.email}")
                    return result
            else:
                result['reason'] = 'no_cookies_in_existing_system'
                logger.info(f"📝 现有系统中没有找到Cookie: {account.email}")
                return result

        except Exception as e:
            result['reason'] = f'existing_system_error: {str(e)}'
            logger.error(f"❌ 现有Cookie系统错误: {account.email} - {e}")
            return result

    def _load_cookies_enhanced(self, account: Account) -> dict:
        """增强版Cookie加载"""
        result = {'success': False, 'reason': '', 'cookies': []}

        try:
            import json
            from pathlib import Path
            from datetime import datetime, timedelta

            # Cookie文件路径
            cookie_dir = Path("data/cookies")
            cookie_dir.mkdir(parents=True, exist_ok=True)
            cookie_file = cookie_dir / f"{account.email}.json"

            if not cookie_file.exists():
                result['reason'] = 'no_cookie_file'
                logger.info(f"📝 没有找到Cookie文件: {account.email}")
                return result

            # 读取Cookie文件
            try:
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    cookies_data = json.load(f)
            except json.JSONDecodeError as e:
                result['reason'] = 'cookie_file_corrupted'
                logger.error(f"❌ Cookie文件损坏: {account.email} - {e}")
                # 删除损坏的文件
                cookie_file.unlink()
                return result

            # 检查Cookie数据完整性
            if not isinstance(cookies_data, dict) or 'cookies' not in cookies_data:
                result['reason'] = 'cookie_data_invalid'
                logger.error(f"❌ Cookie数据格式无效: {account.email}")
                cookie_file.unlink()
                return result

            # 检查Cookie是否过期
            save_time_str = cookies_data.get('save_time')
            if not save_time_str:
                result['reason'] = 'cookie_no_timestamp'
                logger.warning(f"⚠️ Cookie没有时间戳: {account.email}")
                cookie_file.unlink()
                return result

            try:
                save_time = datetime.fromisoformat(save_time_str)
                age_days = (datetime.now() - save_time).days

                if age_days > 7:  # Cookie有效期7天
                    result['reason'] = f'cookie_expired_{age_days}_days'
                    logger.warning(f"⚠️ Cookie已过期 {age_days} 天: {account.email}")
                    cookie_file.unlink()
                    return result
                else:
                    logger.info(f"📅 Cookie年龄: {age_days} 天，仍有效: {account.email}")

            except ValueError as e:
                result['reason'] = 'cookie_timestamp_invalid'
                logger.error(f"❌ Cookie时间戳无效: {account.email} - {e}")
                cookie_file.unlink()
                return result

            # 验证Cookie列表
            cookies = cookies_data.get('cookies', [])
            if not cookies or not isinstance(cookies, list):
                result['reason'] = 'cookie_list_empty'
                logger.warning(f"⚠️ Cookie列表为空: {account.email}")
                return result

            # 过滤有效的Cookie
            valid_cookies = []
            for cookie in cookies:
                if self._validate_cookie(cookie):
                    valid_cookies.append(cookie)

            if not valid_cookies:
                result['reason'] = 'no_valid_cookies'
                logger.warning(f"⚠️ 没有有效的Cookie: {account.email}")
                return result

            result['success'] = True
            result['cookies'] = valid_cookies
            result['reason'] = f'loaded_{len(valid_cookies)}_cookies'
            logger.info(f"✅ 成功加载 {len(valid_cookies)} 个有效Cookie: {account.email}")
            return result

        except Exception as e:
            result['reason'] = f'load_exception: {str(e)}'
            logger.error(f"❌ 加载Cookie异常: {account.email} - {e}")
            return result

    def _validate_cookie(self, cookie: dict) -> bool:
        """验证Cookie格式"""
        try:
            # 检查必需的字段
            required_fields = ['name', 'value', 'domain']
            for field in required_fields:
                if field not in cookie:
                    return False

            # 检查域名是否为新浪相关
            domain = cookie.get('domain', '')
            if 'sina.com' not in domain and 'sinamail.com' not in domain:
                return False

            return True

        except Exception:
            return False

    def _verify_login_status_enhanced(self, driver, account: Account) -> dict:
        """增强版登录状态验证 - 带会话检查"""
        result = {'success': False, 'reason': ''}

        try:
            # 首先检查浏览器会话是否有效
            try:
                current_url = driver.current_url
                page_title = driver.title
            except Exception as session_error:
                result['reason'] = f'session_invalid: {str(session_error)}'
                logger.error(f"❌ 浏览器会话无效: {session_error}")
                return result

            logger.info(f"🔍 验证登录状态 - URL: {current_url}")
            logger.info(f"🔍 页面标题: {page_title}")

            # 检查是否在登录页面
            if "login" in current_url.lower() or "登录" in page_title:
                result['reason'] = 'still_on_login_page'
                return result

            # 检查是否有错误提示
            try:
                from selenium.webdriver.common.by import By
                error_elements = driver.find_elements(By.XPATH,
                    "//*[contains(text(), '账号被锁定') or contains(text(), '账号异常') or contains(text(), '密码错误')]")
                if error_elements:
                    error_text = error_elements[0].text
                    if '账号被锁定' in error_text or '账号异常' in error_text:
                        result['reason'] = 'account_banned'
                        return result
                    elif '密码错误' in error_text:
                        result['reason'] = 'password_error'
                        return result
            except Exception:
                pass

            # 方法1：检查URL是否包含邮箱主页特征
            if "mail.sina.com.cn" in current_url and "login" not in current_url.lower():
                result['success'] = True
                result['reason'] = 'url_verification_passed'
                logger.info(f"✅ URL验证通过: {current_url}")
                return result

            # 方法2：检查页面标题
            if "邮箱" in page_title and "登录" not in page_title:
                result['success'] = True
                result['reason'] = 'title_verification_passed'
                logger.info(f"✅ 标题验证通过: {page_title}")
                return result

            # 方法3：查找写信按钮或用户信息（简化版，避免复杂操作）
            try:
                from selenium.webdriver.common.by import By

                # 简单检查页面源码中是否包含关键词
                page_source = driver.page_source.lower()

                if '写信' in page_source or 'compose' in page_source:
                    result['success'] = True
                    result['reason'] = 'compose_keyword_found'
                    logger.info("✅ 页面包含写信关键词")
                    return result

                if account.email.lower() in page_source:
                    result['success'] = True
                    result['reason'] = 'user_email_found_in_page'
                    logger.info("✅ 页面包含用户邮箱")
                    return result

            except Exception as e:
                logger.debug(f"页面内容检查异常: {e}")

            # 如果在新浪邮箱域名下，认为可能已登录
            if "mail.sina.com.cn" in current_url:
                result['success'] = True
                result['reason'] = 'probably_logged_in_on_sina_domain'
                logger.info("🤔 在新浪邮箱域名下，可能已登录")
                return result

            result['reason'] = 'no_login_indicators_found'
            return result

        except Exception as e:
            result['reason'] = f'verification_exception: {str(e)}'
            logger.error(f"验证登录状态异常: {e}")
            return result

    def _update_account_status(self, account: Account, status: str, reason: str):
        """更新账号状态"""
        try:
            from datetime import datetime

            # 状态映射
            status_mapping = {
                'active': 'active',
                'cookie_expired': 'cookie_expired',
                'cookie_invalid': 'cookie_invalid',
                'banned': 'banned',
                'login_failed': 'login_failed',
                'error': 'error'
            }

            new_status = status_mapping.get(status, 'unknown')

            logger.info(f"📊 更新账号状态: {account.email} -> {new_status} ({reason})")

            # 更新账号对象
            account.status = new_status
            account.last_error = reason
            account.last_check = datetime.now()

            # 如果是被封禁，设置冷却时间
            if new_status == 'banned':
                from datetime import timedelta
                account.cooling_until = datetime.now() + timedelta(hours=24)
                logger.warning(f"⚠️ 账号被封禁，设置24小时冷却: {account.email}")

            # 保存到数据库（如果有数据库管理器）
            if hasattr(self, 'db_manager') and self.db_manager:
                try:
                    # 这里需要根据实际的数据库管理器接口来实现
                    # self.db_manager.update_account_status(account.id, new_status, reason)
                    pass
                except Exception as e:
                    logger.error(f"❌ 更新数据库账号状态失败: {e}")

        except Exception as e:
            logger.error(f"❌ 更新账号状态异常: {e}")

    def _update_cookie_last_used(self, account: Account):
        """更新Cookie最后使用时间"""
        try:
            import json
            from pathlib import Path
            from datetime import datetime

            cookie_dir = Path("data/cookies")
            cookie_file = cookie_dir / f"{account.email}.json"

            if cookie_file.exists():
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    cookies_data = json.load(f)

                cookies_data['last_used'] = datetime.now().isoformat()

                with open(cookie_file, 'w', encoding='utf-8') as f:
                    json.dump(cookies_data, f, ensure_ascii=False, indent=2)

                logger.info(f"✅ 更新Cookie使用时间: {account.email}")

        except Exception as e:
            logger.error(f"❌ 更新Cookie使用时间失败: {e}")

    def _remove_invalid_cookies(self, account: Account):
        """删除无效的Cookie文件"""
        try:
            from pathlib import Path

            cookie_dir = Path("data/cookies")
            cookie_file = cookie_dir / f"{account.email}.json"

            if cookie_file.exists():
                cookie_file.unlink()
                logger.info(f"🗑️ 删除无效Cookie文件: {account.email}")

        except Exception as e:
            logger.error(f"❌ 删除Cookie文件失败: {e}")

    def _verify_login_status(self, driver, account: Account) -> bool:
        """验证登录状态"""
        try:
            current_url = driver.current_url
            logger.info(f"🔍 验证登录状态 - URL: {current_url}")

            # 方法1：检查URL是否包含邮箱主页特征
            if "mail.sina.com.cn" in current_url and "login" not in current_url.lower():
                logger.info("✅ URL验证：已登录")
                return True

            # 方法2：检查页面标题
            page_title = driver.title
            logger.info(f"🔍 页面标题: {page_title}")
            if "邮箱" in page_title and "登录" not in page_title:
                logger.info("✅ 标题验证：已登录")
                return True

            # 方法3：查找写信按钮或用户信息
            try:
                from selenium.webdriver.common.by import By

                # 查找写信相关元素
                write_elements = driver.find_elements(By.XPATH,
                    "//a[contains(text(), '写信')] | //a[contains(text(), '写邮件')] | //a[contains(@href, 'compose')]")
                if write_elements:
                    logger.info("✅ 元素验证：找到写信按钮，已登录")
                    return True

                # 查找用户邮箱地址
                user_elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{account.email}')]")
                if user_elements:
                    logger.info("✅ 元素验证：找到用户信息，已登录")
                    return True

            except Exception as e:
                logger.debug(f"元素查找异常: {e}")

            logger.info("❌ 登录状态验证失败")
            return False

        except Exception as e:
            logger.error(f"验证登录状态异常: {e}")
            return False

    def _load_cookies(self, account: Account) -> list:
        """加载保存的Cookie"""
        try:
            import json
            from pathlib import Path

            # Cookie文件路径
            cookie_dir = Path("data/cookies")
            cookie_dir.mkdir(parents=True, exist_ok=True)
            cookie_file = cookie_dir / f"{account.email}.json"

            if not cookie_file.exists():
                return []

            with open(cookie_file, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)

            # 检查Cookie是否过期（7天）
            from datetime import datetime, timedelta
            save_time = datetime.fromisoformat(cookies_data.get('save_time', '2000-01-01'))
            if datetime.now() - save_time > timedelta(days=7):
                logger.info(f"Cookie已过期: {account.email}")
                return []

            return cookies_data.get('cookies', [])

        except Exception as e:
            logger.error(f"加载Cookie失败: {e}")
            return []

    def _save_cookies(self, driver, account: Account):
        """保存Cookie"""
        try:
            import json
            from pathlib import Path
            from datetime import datetime

            # Cookie文件路径
            cookie_dir = Path("data/cookies")
            cookie_dir.mkdir(parents=True, exist_ok=True)
            cookie_file = cookie_dir / f"{account.email}.json"

            # 获取当前Cookie
            cookies = driver.get_cookies()

            # 保存Cookie和时间戳
            cookies_data = {
                'save_time': datetime.now().isoformat(),
                'account_email': account.email,
                'cookies': cookies
            }

            with open(cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookies_data, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ Cookie保存成功: {account.email}")

        except Exception as e:
            logger.error(f"保存Cookie失败: {e}")
    
    def _create_chrome_options(self, account: Account) -> ChromeOptions:
        """
        创建Chrome选项
        
        Args:
            account: 账号信息
        
        Returns:
            Chrome选项对象
        """
        options = ChromeOptions()
        
        # 基本选项 - 智能窗口模式配置
        window_size = self.browser_config.get('window_size', [1280, 720])
        window_mode = self.browser_config.get('window_mode', '最小化窗口')

        if window_mode == '无头模式':
            # 无头模式 - 完全隐藏，最轻量
            options.add_argument('--headless')
            options.add_argument('--disable-gpu')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            # 无头模式专用优化
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')
            options.add_argument('--disable-images')  # 无头模式可以禁用图片
            options.add_argument('--disable-css')     # 禁用CSS加载
            options.add_argument('--disable-fonts')   # 禁用字体加载
            options.add_argument('--disable-audio')   # 禁用音频
            options.add_argument('--disable-video')   # 禁用视频
            logger.info("🥷 无头模式已启用 - 完全后台运行，最轻量配置")
        elif window_mode == '最小化窗口':
            # 最小化模式 - 小窗口，减少资源占用
            options.add_argument(f'--window-size={window_size[0]},{window_size[1]}')
            options.add_argument('--window-position=0,0')
            options.add_argument('--start-minimized')  # 启动时最小化
            options.add_argument('--disable-extensions')
            options.add_argument('--disable-plugins')

            # 🔧 最小化模式网络修复 - 添加与正常模式相同的网络设置
            options.add_argument('--no-proxy-server')  # 禁用代理服务器
            options.add_argument('--proxy-bypass-list=*')  # 绕过所有代理
            options.add_argument('--disable-proxy-certificate-handler')
            options.add_argument('--ignore-ssl-errors')
            options.add_argument('--ignore-certificate-errors')
            options.add_argument('--ignore-certificate-errors-spki-list')
            options.add_argument('--ignore-certificate-errors-ssl-errors')
            options.add_argument('--allow-running-insecure-content')
            options.add_argument('--disable-web-security')

            # 保留图片，可能需要看验证码
            logger.info(f"📱 最小化窗口模式 - 窗口大小: {window_size[0]}x{window_size[1]} (已优化网络设置)")
        else:
            # 正常窗口模式
            options.add_argument(f'--window-size={window_size[0]},{window_size[1]}')
            logger.info(f"🖥️ 正常窗口模式 - 窗口大小: {window_size[0]}x{window_size[1]}")

        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')

        # SSL和网络连接优化 - 修复SSL握手错误
        options.add_argument('--ignore-ssl-errors')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-certificate-errors-spki-list')
        options.add_argument('--disable-ssl-false-start')
        options.add_argument('--disable-background-networking')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-background-mode')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-sync')
        options.add_argument('--disable-translate')
        options.add_argument('--disable-ipc-flooding-protection')

        # 网络超时和重试设置
        options.add_argument('--aggressive-cache-discard')
        options.add_argument('--disable-hang-monitor')

        # 🔧 额外的网络连接修复
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-infobars')
        options.add_argument('--disable-notifications')
        options.add_argument('--disable-popup-blocking')
        options.add_argument('--disable-prompt-on-repost')
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--ignore-certificate-errors-spki-list')
        options.add_argument('--ignore-certificate-errors-ssl-errors')
        options.add_argument('--ignore-ssl-errors-spki-list')
        options.add_argument('--ignore-ssl-errors-ssl-errors')

        # 🌐 强制网络设置
        options.add_argument('--host-resolver-rules=MAP * ~NOTFOUND , EXCLUDE mail.sina.com.cn')
        options.add_argument('--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer')
        options.add_argument('--enable-features=NetworkService,NetworkServiceLogging')

        # 🔧 代理问题修复
        options.add_argument('--no-proxy-server')  # 禁用代理服务器
        options.add_argument('--proxy-bypass-list=*')  # 绕过所有代理
        options.add_argument('--disable-proxy-certificate-handler')
        options.add_argument('--disable-background-networking')

        # 🔒 绕过安全检查
        options.add_argument('--disable-site-isolation-trials')
        options.add_argument('--disable-features=site-per-process')
        options.add_argument('--disable-features=TranslateUI,BlinkGenPropertyTrees')

        # 极速启动优化参数
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-component-update')
        options.add_argument('--disable-background-downloads')
        options.add_argument('--disable-add-to-shelf')
        options.add_argument('--disable-client-side-phishing-detection')
        options.add_argument('--disable-datasaver-prompt')
        options.add_argument('--disable-domain-reliability')
        options.add_argument('--disable-features=TranslateUI')
        options.add_argument('--disable-ipc-flooding-protection')
        options.add_argument('--disable-prompt-on-repost')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-sync')
        options.add_argument('--disable-translate')
        options.add_argument('--hide-scrollbars')
        options.add_argument('--metrics-recording-only')
        options.add_argument('--mute-audio')
        options.add_argument('--no-pings')
        options.add_argument('--safebrowsing-disable-auto-update')
        options.add_argument('--disable-logging')
        options.add_argument('--disable-dev-tools')
        options.add_argument('--disable-extensions-file-access-check')
        options.add_argument('--disable-extensions-http-throttling')
        options.add_argument('--aggressive-cache-discard')
        options.add_argument('--memory-pressure-off')
        options.add_argument('--max_old_space_size=4096')
        options.add_argument('--disable-prompt-on-repost')
        options.add_argument('--disable-domain-reliability')
        options.add_argument('--disable-component-extensions-with-background-pages')

        # 极速启动优化
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-background-downloads')
        options.add_argument('--disable-component-update')
        options.add_argument('--disable-client-side-phishing-detection')
        options.add_argument('--disable-datasaver-prompt')
        options.add_argument('--disable-desktop-notifications')
        options.add_argument('--disable-device-discovery-notifications')
        options.add_argument('--disable-infobars')
        options.add_argument('--disable-save-password-bubble')
        options.add_argument('--disable-session-crashed-bubble')
        options.add_argument('--disable-translate-new-ux')
        options.add_argument('--disable-features=TranslateUI')
        options.add_argument('--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer')
        options.add_argument('--disable-ipc-flooding-protection')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-features=Translate')
        options.add_argument('--disable-logging')
        options.add_argument('--disable-login-animations')
        options.add_argument('--disable-modal-animations')
        options.add_argument('--disable-new-avatar-menu')
        options.add_argument('--disable-new-profile-management')
        options.add_argument('--disable-password-generation')
        options.add_argument('--disable-permissions-api')
        options.add_argument('--disable-plugins-discovery')
        options.add_argument('--disable-preconnect')
        options.add_argument('--disable-print-preview')
        options.add_argument('--disable-search-engine-choice-screen')
        options.add_argument('--disable-site-isolation-trials')
        options.add_argument('--disable-speech-api')
        options.add_argument('--disable-tab-for-desktop-share')
        options.add_argument('--disable-voice-input')
        options.add_argument('--disable-wake-on-wifi')
        options.add_argument('--disable-web-bluetooth')
        options.add_argument('--disable-webgl')
        options.add_argument('--disable-webrtc')

        # 内存和性能优化
        options.add_argument('--memory-pressure-off')
        options.add_argument('--max_old_space_size=4096')
        options.add_argument('--aggressive-cache-discard')
        options.add_argument('--enable-aggressive-domstorage-flushing')
        options.add_argument('--enable-fast-unload')
        options.add_argument('--process-per-site')
        options.add_argument('--single-process')  # 单进程模式，启动更快
        
        # 设置User-Agent
        user_agent = self.browser_config.get('user_agent')
        if user_agent:
            options.add_argument(f'--user-agent={user_agent}')
        
        # 代理设置
        if account.has_proxy():
            proxy_info = account.get_proxy_info()
            if proxy_info:
                proxy_string = f"{proxy_info['ip']}:{proxy_info['port']}"
                
                # 如果有用户名密码，需要使用插件方式
                if 'username' in proxy_info and 'password' in proxy_info:
                    # 创建代理插件
                    plugin_path = self._create_proxy_plugin(proxy_info)
                    options.add_extension(plugin_path)
                else:
                    options.add_argument(f'--proxy-server=http://{proxy_string}')
                
                logger.info(f"配置代理: {proxy_string}")
        
        # 下载设置
        download_path = self.browser_config.get('download_path', 'data/downloads')
        if not os.path.isabs(download_path):
            project_root = Path(__file__).parent.parent.parent
            download_path = project_root / download_path
        
        download_path = Path(download_path)
        download_path.mkdir(parents=True, exist_ok=True)
        
        prefs = {
            'download.default_directory': str(download_path),
            'download.prompt_for_download': False,
            'download.directory_upgrade': True,
            'safebrowsing.enabled': True
        }
        options.add_experimental_option('prefs', prefs)
        
        # 禁用通知
        options.add_experimental_option('useAutomationExtension', False)
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_argument('--disable-blink-features=AutomationControlled')
        
        return options
    
    def _create_proxy_plugin(self, proxy_info: Dict[str, Any]) -> str:
        """
        创建代理认证插件
        
        Args:
            proxy_info: 代理信息
        
        Returns:
            插件文件路径
        """
        import zipfile
        import tempfile
        
        # 插件代码
        manifest_json = """
        {
            "version": "1.0.0",
            "manifest_version": 2,
            "name": "Chrome Proxy",
            "permissions": [
                "proxy",
                "tabs",
                "unlimitedStorage",
                "storage",
                "<all_urls>",
                "webRequest",
                "webRequestBlocking"
            ],
            "background": {
                "scripts": ["background.js"]
            },
            "minimum_chrome_version":"22.0.0"
        }
        """
        
        background_js = f"""
        var config = {{
                mode: "fixed_servers",
                rules: {{
                  singleProxy: {{
                    scheme: "http",
                    host: "{proxy_info['ip']}",
                    port: parseInt({proxy_info['port']})
                  }},
                  bypassList: ["localhost"]
                }}
              }};

        chrome.proxy.settings.set({{value: config, scope: "regular"}}, function() {{}});

        function callbackFn(details) {{
            return {{
                authCredentials: {{
                    username: "{proxy_info['username']}",
                    password: "{proxy_info['password']}"
                }}
            }};
        }}

        chrome.webRequest.onAuthRequired.addListener(
                    callbackFn,
                    {{urls: ["<all_urls>"]}},
                    ['blocking']
        );
        """
        
        # 创建临时插件文件
        plugin_file = tempfile.NamedTemporaryFile(suffix='.zip', delete=False)
        
        with zipfile.ZipFile(plugin_file.name, 'w') as zf:
            zf.writestr("manifest.json", manifest_json)
            zf.writestr("background.js", background_js)
        
        return plugin_file.name
    
    def get_driver(self, driver_id: str) -> Optional[webdriver.Chrome]:
        """
        获取浏览器驱动
        
        Args:
            driver_id: 驱动ID
        
        Returns:
            浏览器驱动实例或None
        """
        return self.drivers.get(driver_id)
    
    def close_driver(self, driver_id: str) -> bool:
        """
        关闭浏览器驱动
        
        Args:
            driver_id: 驱动ID
        
        Returns:
            是否关闭成功
        """
        try:
            driver = self.drivers.get(driver_id)
            if driver:
                logger.info(f"开始关闭浏览器驱动: {driver_id}")

                # 尝试优雅关闭
                try:
                    # 先关闭所有标签页
                    for handle in driver.window_handles:
                        try:
                            driver.switch_to.window(handle)
                            driver.close()
                        except:
                            pass
                except:
                    pass

                # 退出驱动
                try:
                    driver.quit()
                    logger.info(f"浏览器驱动优雅关闭成功: {driver_id}")
                except Exception as e:
                    logger.warning(f"优雅关闭失败: {e}")

                # 从字典中移除
                del self.drivers[driver_id]
                self.driver_count -= 1
                logger.info(f"浏览器驱动清理完成: {driver_id}")
                return True
            else:
                logger.warning(f"未找到浏览器驱动: {driver_id}")
                return False
        except Exception as e:
            logger.error(f"关闭浏览器驱动失败: {driver_id}, 错误: {e}")
            # 即使关闭失败，也要从字典中移除
            if driver_id in self.drivers:
                del self.drivers[driver_id]
                self.driver_count -= 1
                logger.info(f"强制清理驱动引用: {driver_id}")
            return False
    
    def close_all_drivers(self):
        """关闭所有浏览器驱动"""
        driver_ids = list(self.drivers.keys())
        for driver_id in driver_ids:
            self.close_driver(driver_id)
        
        logger.info("所有浏览器驱动已关闭")
    
    def wait_for_element(self, driver: webdriver.Chrome, by: By, value: str, 
                        timeout: int = 10) -> bool:
        """
        等待元素出现
        
        Args:
            driver: 浏览器驱动
            by: 定位方式
            value: 定位值
            timeout: 超时时间（秒）
        
        Returns:
            是否找到元素
        """
        try:
            WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return True
        except TimeoutException:
            logger.warning(f"等待元素超时: {by}={value}")
            return False
    
    def safe_click(self, driver: webdriver.Chrome, by: By, value: str, 
                   timeout: int = 10) -> bool:
        """
        安全点击元素
        
        Args:
            driver: 浏览器驱动
            by: 定位方式
            value: 定位值
            timeout: 超时时间（秒）
        
        Returns:
            是否点击成功
        """
        try:
            element = WebDriverWait(driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            element.click()
            return True
        except TimeoutException:
            logger.warning(f"点击元素超时: {by}={value}")
            return False
        except Exception as e:
            logger.error(f"点击元素失败: {by}={value}, 错误: {e}")
            return False
    
    def safe_send_keys(self, driver: webdriver.Chrome, by: By, value: str, 
                      text: str, timeout: int = 10, clear_first: bool = True) -> bool:
        """
        安全输入文本
        
        Args:
            driver: 浏览器驱动
            by: 定位方式
            value: 定位值
            text: 要输入的文本
            timeout: 超时时间（秒）
            clear_first: 是否先清空输入框
        
        Returns:
            是否输入成功
        """
        try:
            element = WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            
            if clear_first:
                element.clear()
            
            # 模拟人工输入，添加随机延迟
            for char in text:
                element.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))
            
            return True
        except TimeoutException:
            logger.warning(f"输入文本超时: {by}={value}")
            return False
        except Exception as e:
            logger.error(f"输入文本失败: {by}={value}, 错误: {e}")
            return False
    
    def random_delay(self, min_seconds: float = 1.0, max_seconds: float = 3.0):
        """
        随机延迟
        
        Args:
            min_seconds: 最小延迟时间
            max_seconds: 最大延迟时间
        """
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
    
    def get_driver_count(self) -> int:
        """获取当前驱动数量"""
        return self.driver_count
    
    def get_max_drivers(self) -> int:
        """获取最大驱动数量"""
        return self.max_drivers
