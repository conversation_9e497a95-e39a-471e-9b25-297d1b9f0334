#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
浏览器池轮换机制测试
专门测试浏览器实例的轮换使用情况
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from final_network_speed_solution import FinalNetworkSpeedSolution

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s'
)

logger = logging.getLogger(__name__)

class BrowserPoolRotationTest:
    """浏览器池轮换测试"""
    
    def __init__(self):
        self.solution = FinalNetworkSpeedSolution()
        
    def test_browser_pool_rotation(self):
        """测试浏览器池轮换机制"""
        print("🔄 浏览器池轮换机制测试")
        print("="*80)
        
        # 1. 创建浏览器池
        print("\n📊 第一阶段：创建浏览器池")
        print("-" * 40)
        self.solution.create_optimized_browser_pool()
        
        # 2. 显示浏览器池状态
        self.show_browser_pool_status()
        
        # 3. 测试轮换机制
        print("\n📊 第二阶段：测试浏览器轮换机制")
        print("-" * 40)
        
        # 获取可用账号
        accounts = []
        if self.solution.cookies_dir.exists():
            for cookie_file in self.solution.cookies_dir.glob("*.cookies"):
                account_name = cookie_file.stem.replace("_sina_com", "@sina.com")
                accounts.append(account_name)
        
        if not accounts:
            print("❌ 未找到可用的Cookie文件")
            return
        
        print(f"📋 找到 {len(accounts)} 个可用账号，测试前6个以验证轮换:")
        
        # 测试6个账号，验证轮换机制（3个浏览器实例 × 2轮）
        test_accounts = accounts[:6]
        
        for i, account in enumerate(test_accounts, 1):
            print(f"\n🔄 轮换测试 {i}/6: {account}")
            print("-" * 30)
            
            # 显示当前浏览器池状态
            print(f"   📊 当前轮换索引: {self.solution.current_pool_index}")
            
            # 执行登录测试
            result = self.solution.ultra_fast_cookie_login(account)
            
            # 显示结果
            browser_info = f"浏览器实例 {result.get('browser_index', -1) + 1}" if result.get('browser_index', -1) >= 0 else "临时实例"
            print(f"   🎯 使用: {browser_info}")
            print(f"   📊 结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
            print(f"   ⏱️ 耗时: {result['times'].get('total', 0):.3f}秒")
            
            # 显示浏览器池使用统计
            self.show_browser_usage_stats()
            
            # 短暂延迟，便于观察
            time.sleep(0.5)
        
        # 4. 最终统计
        print("\n📊 第三阶段：浏览器池使用统计")
        print("-" * 40)
        self.show_final_browser_stats()
        
        # 5. 清理浏览器池
        print("\n🧹 清理浏览器池...")
        self.solution.cleanup_browser_pool()
        print("✅ 浏览器池清理完成")
    
    def show_browser_pool_status(self):
        """显示浏览器池状态"""
        print(f"\n📊 浏览器池状态:")
        print(f"   池大小: {len(self.solution.browser_pool)}")
        print(f"   当前轮换索引: {self.solution.current_pool_index}")
        
        for i, browser_info in enumerate(self.solution.browser_pool):
            status = "使用中" if browser_info['in_use'] else "空闲"
            created_time = browser_info.get('created_at', 0)
            print(f"   浏览器 {i+1}: {status} (创建于 {time.strftime('%H:%M:%S', time.localtime(created_time))})")
    
    def show_browser_usage_stats(self):
        """显示浏览器使用统计"""
        usage_count = {}
        for i, browser_info in enumerate(self.solution.browser_pool):
            last_used = browser_info.get('last_used', 0)
            if last_used > 0:
                usage_count[i] = time.strftime('%H:%M:%S', time.localtime(last_used))
        
        if usage_count:
            print(f"   📈 最近使用时间: {usage_count}")
    
    def show_final_browser_stats(self):
        """显示最终浏览器统计"""
        print("📈 浏览器池最终统计:")
        
        total_usage = 0
        for i, browser_info in enumerate(self.solution.browser_pool):
            last_used = browser_info.get('last_used', 0)
            return_time = browser_info.get('return_time', 0)
            created_time = browser_info.get('created_at', 0)
            
            if last_used > 0:
                total_usage += 1
                usage_duration = return_time - last_used if return_time > last_used else 0
                print(f"   浏览器 {i+1}:")
                print(f"      创建时间: {time.strftime('%H:%M:%S', time.localtime(created_time))}")
                print(f"      最后使用: {time.strftime('%H:%M:%S', time.localtime(last_used))}")
                if return_time > 0:
                    print(f"      返回时间: {time.strftime('%H:%M:%S', time.localtime(return_time))}")
                    print(f"      使用时长: {usage_duration:.3f}秒")
            else:
                print(f"   浏览器 {i+1}: 未使用")
        
        print(f"\n📊 总体统计:")
        print(f"   总浏览器数: {len(self.solution.browser_pool)}")
        print(f"   已使用数量: {total_usage}")
        print(f"   使用率: {(total_usage / len(self.solution.browser_pool) * 100):.1f}%")
        
        # 验证轮换机制
        if total_usage == len(self.solution.browser_pool):
            print("   ✅ 轮换机制正常：所有浏览器实例都被使用")
        elif total_usage > 0:
            print("   ⚠️ 轮换机制部分工作：部分浏览器实例被使用")
        else:
            print("   ❌ 轮换机制异常：没有浏览器实例被使用")
    
    def test_concurrent_usage(self):
        """测试并发使用情况"""
        print("\n🔄 并发使用测试")
        print("="*80)
        
        # 创建浏览器池
        self.solution.create_optimized_browser_pool()
        
        # 模拟并发获取浏览器
        print("📊 模拟并发获取浏览器实例:")
        
        acquired_browsers = []
        
        for i in range(5):  # 尝试获取5个浏览器（池中只有3个）
            print(f"\n🔄 第 {i+1} 次获取:")
            driver, browser_index = self.solution.get_browser_from_pool()
            acquired_browsers.append((driver, browser_index))
            
            if browser_index >= 0:
                print(f"   ✅ 获取到浏览器实例 {browser_index + 1}")
            else:
                print(f"   ⚠️ 创建了临时浏览器实例")
            
            # 显示当前池状态
            in_use_count = sum(1 for b in self.solution.browser_pool if b['in_use'])
            print(f"   📊 池中使用状态: {in_use_count}/{len(self.solution.browser_pool)} 个在使用")
        
        # 返回所有浏览器
        print(f"\n🔄 返回所有浏览器到池中:")
        for i, (driver, browser_index) in enumerate(acquired_browsers):
            print(f"   🔄 返回第 {i+1} 个浏览器")
            self.solution.return_browser_to_pool(driver, browser_index)
        
        # 最终状态
        in_use_count = sum(1 for b in self.solution.browser_pool if b['in_use'])
        print(f"\n📊 最终池状态: {in_use_count}/{len(self.solution.browser_pool)} 个在使用")
        
        # 清理
        self.solution.cleanup_browser_pool()

if __name__ == "__main__":
    print("🔄 启动浏览器池轮换机制测试...")
    
    tester = BrowserPoolRotationTest()
    
    # 测试轮换机制
    tester.test_browser_pool_rotation()
    
    print("\n" + "="*80)
    
    # 测试并发使用
    tester.test_concurrent_usage()
    
    print("\n✅ 浏览器池轮换测试完成！")
