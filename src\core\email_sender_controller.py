#!/usr/bin/env python3
"""
邮件发送控制器 - 重新架构版本
负责实际的邮件发送执行，与任务队列管理器协作
"""

import time
import threading
import queue
from datetime import datetime
from typing import List, Dict, Any, Optional, Callable

from src.core.task_queue_manager import TaskQueueManager, EmailTask, TaskStatus
from src.core.multi_browser_manager import MultiBrowserManager, SendingConfig
from src.core.sina_ultra_fast_sender_final import SinaUltraFastSenderFinal
from src.models.send_record import SendRecord, SendRecordManager, SendStatus
from src.utils.logger import setup_logger

logger = setup_logger("INFO")

class EmailSenderController:
    """邮件发送控制器"""
    
    def __init__(self, task_manager: TaskQueueManager, sending_config: SendingConfig, db_path: str = None):
        self.task_manager = task_manager
        self.sending_config = sending_config

        # 浏览器管理器
        self.browser_manager = None
        self.accounts = []

        # 发送记录管理器
        if db_path:
            from src.models.database import DatabaseManager
            db_manager = DatabaseManager(db_path)
            self.record_manager = SendRecordManager(db_manager)
        else:
            self.record_manager = None

        # 发送状态
        self.is_sending = False
        self.is_stopping = False
        self.worker_threads: List[threading.Thread] = []

        # 发送统计
        self.send_stats = {
            'sent_success': 0,
            'sent_failed': 0,
            'send_rate': 0.0,  # 每分钟发送数量
            'start_time': None,
            'last_send_time': None
        }

        # 线程锁
        self.lock = threading.RLock()

        # 回调函数
        self.on_send_progress = None
        self.on_send_stats_updated = None
        self.on_record_created = None  # 新增：记录创建回调

        logger.info("邮件发送控制器初始化完成")
    
    def initialize(self, accounts: List[Any]) -> bool:
        """初始化发送器"""
        try:
            logger.info("初始化邮件发送控制器...")
            
            self.accounts = accounts
            if not self.accounts:
                logger.error("没有可用的账号")
                return False
            
            # 创建浏览器管理器
            self.browser_manager = MultiBrowserManager(self.sending_config)
            
            # 初始化浏览器
            if not self.browser_manager.initialize_browsers():
                logger.error("浏览器初始化失败")
                return False
            
            # 设置账号队列
            self.browser_manager.set_account_queue(self.accounts)
            
            logger.info(f"发送控制器初始化成功，{len(self.accounts)} 个账号，{len(self.browser_manager.browsers)} 个浏览器")
            return True
            
        except Exception as e:
            logger.error(f"发送控制器初始化失败: {e}")
            return False
    
    def start_sending(self) -> bool:
        """开始发送"""
        if self.is_sending:
            logger.warning("发送器已在运行中")
            return True
        
        if not self.browser_manager:
            logger.error("发送器未初始化")
            return False
        
        try:
            logger.info("启动邮件发送...")
            
            self.is_sending = True
            self.is_stopping = False
            self.send_stats['start_time'] = time.time()
            
            # 启动工作线程
            num_workers = min(self.sending_config.max_browsers, len(self.browser_manager.browsers))
            
            for i in range(num_workers):
                worker_thread = threading.Thread(
                    target=self._worker_thread,
                    args=(i,),
                    daemon=True,
                    name=f"EmailSender-Worker-{i}"
                )
                worker_thread.start()
                self.worker_threads.append(worker_thread)
            
            logger.info(f"邮件发送已启动，{len(self.worker_threads)} 个工作线程")
            return True
            
        except Exception as e:
            logger.error(f"启动发送失败: {e}")
            self.is_sending = False
            return False
    
    def stop_sending(self):
        """停止发送"""
        if not self.is_sending:
            return
        
        logger.info("正在停止邮件发送...")
        self.is_stopping = True
        self.is_sending = False
        
        # 等待工作线程结束
        for thread in self.worker_threads:
            if thread.is_alive():
                thread.join(timeout=5)
        
        self.worker_threads.clear()
        logger.info("邮件发送已停止")
    
    def pause_sending(self):
        """暂停发送"""
        self.task_manager.pause()
        logger.info("邮件发送已暂停")
    
    def resume_sending(self):
        """恢复发送"""
        self.task_manager.resume()
        logger.info("邮件发送已恢复")
    
    def _worker_thread(self, worker_id: int):
        """工作线程"""
        logger.info(f"工作线程 {worker_id} 启动")
        
        # 获取浏览器实例
        browser_instances = list(self.browser_manager.browsers.values())
        if worker_id >= len(browser_instances):
            logger.error(f"工作线程 {worker_id} 没有对应的浏览器实例")
            return
        
        browser_instance = browser_instances[worker_id]
        last_send_time = 0
        
        while self.is_sending and not self.is_stopping:
            try:
                # 检查是否暂停
                if self.task_manager.is_paused:
                    time.sleep(1)
                    continue
                
                # 从任务队列获取任务
                try:
                    task = self.task_manager.task_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 检查发送间隔
                current_time = time.time()
                if current_time - last_send_time < self.sending_config.send_interval:
                    # 将任务放回队列
                    self.task_manager.task_queue.put(task)
                    time.sleep(0.1)
                    continue
                
                # 检查浏览器状态
                if not self._check_browser_ready(browser_instance):
                    # 将任务放回队列
                    self.task_manager.task_queue.put(task)
                    time.sleep(1)
                    continue
                
                # 执行发送
                success = self._send_email(browser_instance, task)
                last_send_time = current_time
                
                # 更新统计
                with self.lock:
                    if success:
                        self.send_stats['sent_success'] += 1
                        self.task_manager.update_task_status(task.task_id, TaskStatus.COMPLETED)
                    else:
                        self.send_stats['sent_failed'] += 1
                        self.task_manager.update_task_status(task.task_id, TaskStatus.FAILED, "发送失败")
                    
                    self.send_stats['last_send_time'] = current_time
                    self._update_send_rate()
                
                # 触发进度回调
                if self.on_send_progress:
                    self.on_send_progress(task, success)
                
                if self.on_send_stats_updated:
                    self.on_send_stats_updated(self.send_stats.copy())
                
            except Exception as e:
                logger.error(f"工作线程 {worker_id} 处理任务异常: {e}")
                time.sleep(1)
        
        logger.info(f"工作线程 {worker_id} 结束")
    
    def _check_browser_ready(self, browser_instance) -> bool:
        """检查浏览器是否就绪"""
        try:
            if not browser_instance or not browser_instance.driver:
                return False
            
            # 检查浏览器会话
            current_url = browser_instance.driver.current_url
            if "mail.sina.com" not in current_url:
                # 尝试重新登录
                return self._recover_browser_session(browser_instance)
            
            return True
            
        except Exception as e:
            logger.warning(f"检查浏览器状态失败: {e}")
            return self._recover_browser_session(browser_instance)
    
    def _recover_browser_session(self, browser_instance) -> bool:
        """恢复浏览器会话"""
        try:
            logger.info(f"恢复浏览器会话: {browser_instance.browser_id}")
            
            # 重新加载账号
            if browser_instance.current_account:
                if self.browser_manager.load_account_cookies(browser_instance, browser_instance.current_account):
                    logger.info(f"浏览器会话恢复成功: {browser_instance.browser_id}")
                    return True
            
            # 尝试切换账号
            if self.browser_manager.switch_account_if_needed(browser_instance, force_switch=True):
                logger.info(f"浏览器切换账号成功: {browser_instance.browser_id}")
                return True
            
            logger.error(f"浏览器会话恢复失败: {browser_instance.browser_id}")
            return False
            
        except Exception as e:
            logger.error(f"恢复浏览器会话异常: {e}")
            return False
    
    def _send_email(self, browser_instance, task: EmailTask) -> bool:
        """发送邮件"""
        send_record = None
        start_time = time.time()

        try:
            logger.info(f"开始发送邮件: {task.task_id} -> {task.to_email}")

            # 更新任务状态
            self.task_manager.update_task_status(task.task_id, TaskStatus.PROCESSING)

            # 创建发送记录
            if self.record_manager:
                send_record = SendRecord(
                    task_id=task.task_id,
                    from_email=browser_instance.current_account.email if browser_instance.current_account else "",
                    to_email=task.to_email,
                    subject=task.subject,
                    content=task.content,
                    content_type=task.content_type,
                    status=SendStatus.SENDING,
                    browser_id=browser_instance.browser_id,
                    proxy_ip=getattr(browser_instance, 'proxy_ip', None),
                    create_time=datetime.now()
                )
                record_id = self.record_manager.add_record(send_record)
                send_record.id = record_id

            # 创建发送器
            sender = SinaUltraFastSenderFinal(browser_instance.driver)

            # 执行发送
            success = sender.send_email_ultra_fast(task.to_email, task.subject, task.content)

            # 计算响应时间
            response_time = time.time() - start_time

            # 更新发送记录
            if self.record_manager and send_record:
                send_record.status = SendStatus.SUCCESS if success else SendStatus.FAILED
                send_record.response_time = response_time
                send_record.send_time = datetime.now()
                if not success:
                    send_record.error_msg = "发送失败"

                self.record_manager.update_record(send_record)

                # 触发记录创建回调
                if self.on_record_created:
                    self.on_record_created(send_record)

            if success:
                logger.info(f"邮件发送成功: {task.task_id} ({response_time:.2f}s)")

                # 更新账号发送次数到数据库
                if hasattr(browser_instance, 'current_account') and browser_instance.current_account:
                    try:
                        from src.models.account import AccountManager
                        from src.models.database import DatabaseManager

                        db_manager = DatabaseManager()
                        account_manager = AccountManager(db_manager)

                        # 根据邮箱获取账号ID并更新发送次数
                        account = account_manager.get_account_by_email(browser_instance.current_account.email)
                        if account and account.id:
                            account_manager.increment_send_count(account.id)
                            logger.info(f"✅ 已更新账号发送次数: {account.email}")
                    except Exception as e:
                        logger.error(f"❌ 更新账号发送次数失败: {e}")
            else:
                logger.error(f"邮件发送失败: {task.task_id}")

            return success

        except Exception as e:
            logger.error(f"发送邮件异常: {task.task_id} - {e}")

            # 更新失败记录
            if self.record_manager and send_record:
                send_record.status = SendStatus.FAILED
                send_record.error_msg = str(e)
                send_record.response_time = time.time() - start_time
                send_record.send_time = datetime.now()
                self.record_manager.update_record(send_record)

                # 触发记录创建回调
                if self.on_record_created:
                    self.on_record_created(send_record)

            return False
    
    def _update_send_rate(self):
        """更新发送速率"""
        if not self.send_stats['start_time']:
            return
        
        elapsed_time = time.time() - self.send_stats['start_time']
        if elapsed_time > 0:
            total_sent = self.send_stats['sent_success'] + self.send_stats['sent_failed']
            self.send_stats['send_rate'] = (total_sent / elapsed_time) * 60  # 每分钟发送数量
    
    def get_send_stats(self) -> Dict[str, Any]:
        """获取发送统计"""
        with self.lock:
            stats = self.send_stats.copy()

            # 如果有记录管理器，添加数据库统计
            if self.record_manager:
                try:
                    db_stats = self.record_manager.get_statistics()
                    stats.update({
                        'total_records': db_stats.get('total', 0),
                        'success_records': db_stats.get('success', 0),
                        'failed_records': db_stats.get('failed', 0),
                        'success_rate': db_stats.get('success_rate', 0.0)
                    })
                except Exception as e:
                    logger.error(f"获取数据库统计失败: {e}")

            return stats

    def get_recent_records(self, limit: int = 100) -> List[SendRecord]:
        """获取最近的发送记录"""
        if self.record_manager:
            try:
                # 使用日期范围获取最近记录
                from datetime import datetime, timedelta
                end_date = datetime.now()
                start_date = end_date - timedelta(days=7)  # 最近7天
                return self.record_manager.get_records_by_date_range(start_date, end_date)
            except Exception as e:
                logger.error(f"获取最近记录失败: {e}")
        return []

    def get_statistics_by_date(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """按日期获取统计信息"""
        if self.record_manager:
            try:
                return self.record_manager.get_statistics(start_date, end_date)
            except Exception as e:
                logger.error(f"获取日期统计失败: {e}")
        return {}
    
    def cleanup(self):
        """清理资源"""
        self.stop_sending()
        
        if self.browser_manager:
            self.browser_manager.cleanup()
            self.browser_manager = None
        
        logger.info("发送控制器资源已清理")
