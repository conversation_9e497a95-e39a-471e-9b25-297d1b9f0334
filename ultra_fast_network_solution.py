#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
超快速网络连接解决方案
基于性能分析结果的完全优化版本
"""

import sys
import time
import logging
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s'
)

logger = logging.getLogger(__name__)

class UltraFastNetworkSolution:
    """超快速网络连接解决方案"""
    
    def __init__(self):
        self.cookies_dir = Path("data/cookies")
        self.driver_cache = None  # 驱动缓存
        self.chrome_options_cache = None  # 选项缓存
        
    def create_lightning_fast_options(self) -> ChromeOptions:
        """创建闪电般快速的Chrome选项"""
        if self.chrome_options_cache:
            return self.chrome_options_cache
            
        options = ChromeOptions()
        
        logger.info("⚡ 创建闪电般快速Chrome选项...")
        
        # 1. 极速启动优化
        ultra_speed_args = [
            '--no-sandbox',                        # 禁用沙箱
            '--disable-dev-shm-usage',             # 禁用/dev/shm
            '--disable-gpu',                       # 禁用GPU
            '--disable-software-rasterizer',       # 禁用软件光栅化
            '--disable-background-timer-throttling', # 禁用后台定时器节流
            '--disable-backgrounding-occluded-windows', # 禁用后台窗口
            '--disable-renderer-backgrounding',    # 禁用渲染器后台
            '--disable-hang-monitor',              # 禁用挂起监控
            '--disable-prompt-on-repost',          # 禁用重新提交提示
            '--disable-domain-reliability',        # 禁用域名可靠性
            '--disable-component-update',          # 禁用组件更新
            '--disable-background-downloads',      # 禁用后台下载
            '--disable-client-side-phishing-detection', # 禁用钓鱼检测
        ]
        
        # 2. 网络连接优化
        network_speed_args = [
            '--no-proxy-server',                   # 禁用代理服务器
            '--proxy-bypass-list=*',               # 绕过所有代理
            '--disable-proxy-certificate-handler', # 禁用代理证书处理
            '--disable-background-networking',     # 禁用后台网络
            '--disable-default-apps',              # 禁用默认应用
            '--disable-sync',                      # 禁用同步
            '--disable-translate',                 # 禁用翻译
            '--disable-plugins',                   # 禁用插件
            '--disable-extensions',                # 禁用扩展
            '--disable-component-extensions-with-background-pages', # 禁用后台扩展
        ]
        
        # 3. SSL和安全优化
        ssl_speed_args = [
            '--ignore-ssl-errors',                 # 忽略SSL错误
            '--ignore-certificate-errors',         # 忽略证书错误
            '--ignore-certificate-errors-spki-list', # 忽略证书错误列表
            '--allow-running-insecure-content',    # 允许不安全内容
            '--disable-ssl-false-start',           # 禁用SSL假启动
            '--disable-web-security',              # 禁用Web安全
        ]
        
        # 4. 内存和资源优化
        memory_speed_args = [
            '--memory-pressure-off',               # 关闭内存压力检测
            '--max_old_space_size=2048',          # 限制内存使用
            '--disable-features=VizDisplayCompositor', # 禁用显示合成器
            '--disable-ipc-flooding-protection',   # 禁用IPC洪水保护
        ]
        
        # 5. 页面加载优化
        loading_speed_args = [
            '--disable-images',                    # 禁用图片加载
            '--blink-settings=imagesEnabled=false', # 禁用图片
            '--disable-java',                      # 禁用Java
            '--disable-flash',                     # 禁用Flash
        ]
        
        # 合并所有参数
        all_args = (ultra_speed_args + network_speed_args + 
                   ssl_speed_args + memory_speed_args + loading_speed_args)
        
        for arg in all_args:
            options.add_argument(arg)
        
        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        # 设置窗口大小
        options.add_argument('--window-size=1280,720')
        
        # 禁用自动化标识
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 缓存选项
        self.chrome_options_cache = options
        
        logger.info(f"   ✅ 配置了 {len(all_args)} 个超速优化参数")
        return options
    
    def get_cached_driver_service(self) -> Service:
        """获取缓存的驱动服务"""
        if not self.driver_cache:
            logger.info("🔧 初始化Chrome驱动缓存...")
            driver_path = ChromeDriverManager().install()
            self.driver_cache = Service(driver_path)
            logger.info("✅ Chrome驱动缓存完成")
        return self.driver_cache
    
    def load_cookies_ultra_fast(self, account_email: str) -> Optional[List[Dict]]:
        """超快速加载cookies"""
        try:
            # 转换邮箱格式为文件名格式
            file_name = account_email.replace("@sina.com", "_sina_com.cookies")
            cookie_file = self.cookies_dir / file_name
            
            if not cookie_file.exists() or cookie_file.stat().st_size == 0:
                return None
            
            # 读取文件内容
            with open(cookie_file, 'r', encoding='utf-8') as f:
                file_content = f.read().strip()
            
            if not file_content:
                return None
            
            # 尝试解密
            try:
                from src.utils.encryption import get_encryption_manager
                encryption_manager = get_encryption_manager()
                decrypted_content = encryption_manager.decrypt(file_content)
                if decrypted_content:
                    cookies_data = json.loads(decrypted_content)
                else:
                    cookies_data = json.loads(file_content)
            except:
                cookies_data = json.loads(file_content)
            
            # 提取cookies列表
            if isinstance(cookies_data, dict) and 'cookies' in cookies_data:
                cookies = cookies_data['cookies']
            elif isinstance(cookies_data, list):
                cookies = cookies_data
            else:
                return None
            
            logger.info(f"⚡ 超快加载Cookie: {account_email} ({len(cookies)} 个)")
            return cookies
            
        except Exception as e:
            logger.error(f"❌ Cookie加载失败: {account_email} - {e}")
            return None
    
    def ultra_fast_login_test(self, account_email: str) -> Dict:
        """超快速登录测试"""
        logger.info(f"🚀 超快速登录测试: {account_email}")
        
        result = {
            'account': account_email,
            'success': False,
            'times': {},
            'details': [],
            'errors': []
        }
        
        # 1. 超快速加载cookies
        start_time = time.time()
        cookies = self.load_cookies_ultra_fast(account_email)
        if not cookies:
            result['errors'].append("无法加载cookies")
            return result
        
        cookie_load_time = time.time() - start_time
        result['times']['cookie_load'] = cookie_load_time
        result['details'].append(f"⚡ Cookie加载: {cookie_load_time:.3f}秒 ({len(cookies)} 个)")
        
        driver = None
        try:
            # 2. 超快速创建浏览器
            start_time = time.time()
            
            options = self.create_lightning_fast_options()
            service = self.get_cached_driver_service()
            driver = webdriver.Chrome(service=service, options=options)
            
            browser_creation_time = time.time() - start_time
            result['times']['browser_creation'] = browser_creation_time
            result['details'].append(f"⚡ 浏览器创建: {browser_creation_time:.3f}秒")
            
            # 3. 超快速页面导航
            start_time = time.time()
            driver.set_page_load_timeout(2)  # 极短超时
            driver.implicitly_wait(0.5)
            
            try:
                driver.get("https://mail.sina.com.cn/")
            except:
                pass  # 忽略超时，继续处理
            
            navigation_time = time.time() - start_time
            result['times']['navigation'] = navigation_time
            result['details'].append(f"⚡ 页面导航: {navigation_time:.3f}秒")
            
            # 4. 超快速应用cookies
            start_time = time.time()
            driver.delete_all_cookies()
            
            applied_count = 0
            for cookie in cookies:
                try:
                    clean_cookie = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie.get('domain', '.sina.com.cn'),
                        'path': cookie.get('path', '/'),
                    }
                    driver.add_cookie(clean_cookie)
                    applied_count += 1
                except:
                    pass  # 忽略失败的cookie
            
            cookie_time = time.time() - start_time
            result['times']['cookie_application'] = cookie_time
            result['details'].append(f"⚡ Cookie应用: {cookie_time:.3f}秒 ({applied_count}/{len(cookies)})")
            
            # 5. 超快速登录检查
            start_time = time.time()
            
            # 刷新页面以应用cookies
            try:
                driver.refresh()
                time.sleep(0.5)  # 短暂等待
            except:
                pass
            
            # 检查登录状态
            try:
                page_source = driver.page_source
                login_indicators = ['写信', '收件箱', '发件箱', '草稿箱', 'compose', 'inbox', '退出', 'logout']
                found_indicators = [indicator for indicator in login_indicators if indicator in page_source]
                
                is_logged_in = len(found_indicators) > 0
                result['success'] = is_logged_in
                result['login_indicators'] = found_indicators
                
                login_check_time = time.time() - start_time
                result['times']['login_check'] = login_check_time
                
                if is_logged_in:
                    result['details'].append(f"✅ 登录检查: {login_check_time:.3f}秒 - 找到: {found_indicators}")
                else:
                    result['details'].append(f"⚠️ 登录检查: {login_check_time:.3f}秒 - 未找到登录指示器")
                    
            except Exception as e:
                login_check_time = time.time() - start_time
                result['times']['login_check'] = login_check_time
                result['details'].append(f"❌ 登录检查异常: {login_check_time:.3f}秒 - {e}")
            
            # 6. 计算总时间
            total_time = sum(result['times'].values())
            result['times']['total'] = total_time
            result['details'].append(f"⚡ 总耗时: {total_time:.3f}秒")
            
            logger.info(f"   🎯 测试完成: {'成功' if result['success'] else '失败'} (总耗时: {total_time:.3f}秒)")
            
            return result
            
        except Exception as e:
            logger.error(f"   ❌ 测试异常: {e}")
            result['errors'].append(f"测试异常: {str(e)}")
            return result
            
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass
    
    def run_ultra_fast_test_suite(self):
        """运行超快速测试套件"""
        print("⚡ 超快速网络连接解决方案测试")
        print("="*80)
        
        # 获取可用账号
        accounts = []
        if self.cookies_dir.exists():
            for cookie_file in self.cookies_dir.glob("*.cookies"):
                account_name = cookie_file.stem.replace("_sina_com", "@sina.com")
                accounts.append(account_name)
        
        if not accounts:
            print("❌ 未找到可用的Cookie文件")
            return
        
        print(f"📋 找到 {len(accounts)} 个可用账号，测试前3个:")
        
        test_results = []
        
        # 测试前3个账号
        for i, account in enumerate(accounts[:3], 1):
            print(f"\n⚡ 超快测试 {i}/3: {account}")
            print("-" * 50)
            
            result = self.ultra_fast_login_test(account)
            
            # 显示结果
            for detail in result['details']:
                print(f"   {detail}")
            
            if result['errors']:
                for error in result['errors']:
                    print(f"   ❌ {error}")
            
            test_results.append(result)
        
        # 性能分析
        self.analyze_ultra_fast_performance(test_results)
    
    def analyze_ultra_fast_performance(self, results: List[Dict]):
        """分析超快速性能"""
        print(f"\n⚡ 超快速性能分析")
        print("="*80)
        
        if not results:
            print("❌ 没有测试结果")
            return
        
        # 统计成功率
        successful = [r for r in results if r['success']]
        success_rate = len(successful) / len(results) * 100
        
        print(f"📊 总体统计:")
        print(f"   测试账号数: {len(results)}")
        print(f"   成功率: {success_rate:.1f}% ({len(successful)}/{len(results)})")
        
        if successful:
            # 分析时间分布
            phases = ['cookie_load', 'browser_creation', 'navigation', 'cookie_application', 'login_check', 'total']
            avg_times = {}
            
            for phase in phases:
                times = [r['times'].get(phase, 0) for r in successful if phase in r['times']]
                if times:
                    avg_times[phase] = sum(times) / len(times)
            
            print(f"\n⚡ 平均耗时分析:")
            for phase, avg_time in avg_times.items():
                print(f"   {phase}: {avg_time:.3f}秒")
            
            # 性能改进建议
            total_avg = avg_times.get('total', 0)
            browser_avg = avg_times.get('browser_creation', 0)
            
            print(f"\n💡 性能优化建议:")
            if total_avg < 5:
                print("   🎉 性能优秀！总耗时已控制在5秒以内")
            elif total_avg < 10:
                print("   ✅ 性能良好！总耗时已控制在10秒以内")
            else:
                print("   ⚠️ 仍需优化，建议进一步减少浏览器创建时间")
            
            if browser_avg > 5:
                print("   🔧 建议：使用浏览器实例池减少创建时间")
            
            print(f"\n🚀 相比原始方案改进:")
            original_time = 11.82  # 原始首次连接时间
            if total_avg > 0:
                improvement = ((original_time - total_avg) / original_time) * 100
                print(f"   速度提升: {improvement:.1f}%")
                print(f"   时间节省: {original_time - total_avg:.3f}秒")

if __name__ == "__main__":
    print("⚡ 启动超快速网络连接解决方案测试...")
    
    solution = UltraFastNetworkSolution()
    solution.run_ultra_fast_test_suite()
    
    print("\n✅ 超快速测试完成！")
