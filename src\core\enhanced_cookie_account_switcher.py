#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的Cookie账号切换器
在同一浏览器下智能切换Cookie登录账号，支持无缝切换和状态监控
"""

import time
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from loguru import logger

from src.models.account import Account
from src.core.ultra_speed_cookie_manager import UltraSpeedCookieManager


@dataclass
class AccountSwitchResult:
    """账号切换结果"""
    success: bool
    account_email: str
    switch_time: float
    error_message: Optional[str] = None
    login_status: str = "unknown"  # logged_in, logged_out, error


@dataclass
class BrowserAccountState:
    """浏览器账号状态"""
    browser_id: int
    current_account: Optional[Account] = None
    available_accounts: List[Account] = None
    account_cookies: Dict[str, List[Dict]] = None  # 账号Cookie缓存
    last_switch_time: float = 0
    switch_count: int = 0
    failed_switches: int = 0
    
    def __post_init__(self):
        if self.available_accounts is None:
            self.available_accounts = []
        if self.account_cookies is None:
            self.account_cookies = {}


class EnhancedCookieAccountSwitcher:
    """增强的Cookie账号切换器"""
    
    def __init__(self):
        self.logger = logger
        self.cookie_manager = UltraSpeedCookieManager()
        
        # 浏览器状态管理
        self.browser_states: Dict[int, BrowserAccountState] = {}
        
        # 切换配置
        self.switch_timeout = 30  # 切换超时时间（秒）
        self.retry_attempts = 3   # 重试次数
        self.switch_interval = 2  # 切换间隔（秒）
        
        # 新浪邮箱相关配置
        self.sina_login_url = "https://mail.sina.com.cn"
        self.sina_compose_url = "https://mail.sina.com.cn/classic/index.php?from=navigation#compose"
    
    def initialize_browser_accounts(
        self,
        browser_id: int,
        driver: webdriver.Chrome,
        accounts: List[Account]
    ) -> bool:
        """
        初始化浏览器账号管理
        
        Args:
            browser_id: 浏览器ID
            driver: WebDriver实例
            accounts: 可用账号列表
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info(f"🔧 初始化浏览器{browser_id}账号管理，账号数量：{len(accounts)}")
            
            # 创建浏览器状态
            self.browser_states[browser_id] = BrowserAccountState(
                browser_id=browser_id,
                available_accounts=accounts.copy()
            )
            
            # 预加载所有账号的Cookie
            success_count = 0
            for account in accounts:
                if self._preload_account_cookies(browser_id, account):
                    success_count += 1
            
            self.logger.info(f"✅ 浏览器{browser_id}账号初始化完成，成功预加载{success_count}/{len(accounts)}个账号Cookie")
            
            # 设置第一个账号为当前账号
            if accounts:
                self.browser_states[browser_id].current_account = accounts[0]
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"初始化浏览器{browser_id}账号管理失败: {e}")
            return False
    
    def switch_account(
        self,
        browser_id: int,
        driver: webdriver.Chrome,
        target_account: Account
    ) -> AccountSwitchResult:
        """
        切换到指定账号
        
        Args:
            browser_id: 浏览器ID
            driver: WebDriver实例
            target_account: 目标账号
            
        Returns:
            AccountSwitchResult: 切换结果
        """
        start_time = time.time()
        
        try:
            if browser_id not in self.browser_states:
                return AccountSwitchResult(
                    success=False,
                    account_email=target_account.email,
                    switch_time=0,
                    error_message="浏览器状态未初始化"
                )
            
            browser_state = self.browser_states[browser_id]
            
            # 检查是否已经是目标账号
            if (browser_state.current_account and 
                browser_state.current_account.email == target_account.email):
                self.logger.info(f"浏览器{browser_id}已经是目标账号{target_account.email}，无需切换")
                return AccountSwitchResult(
                    success=True,
                    account_email=target_account.email,
                    switch_time=0,
                    login_status="already_logged_in"
                )
            
            self.logger.info(f"🔄 浏览器{browser_id}开始切换账号：{target_account.email}")
            
            # 执行切换
            for attempt in range(self.retry_attempts):
                try:
                    # 方法1：尝试Cookie切换
                    if self._switch_by_cookies(browser_id, driver, target_account):
                        switch_time = time.time() - start_time
                        browser_state.current_account = target_account
                        browser_state.last_switch_time = time.time()
                        browser_state.switch_count += 1
                        
                        self.logger.info(f"✅ 浏览器{browser_id}Cookie切换成功：{target_account.email}，耗时{switch_time:.2f}秒")
                        
                        return AccountSwitchResult(
                            success=True,
                            account_email=target_account.email,
                            switch_time=switch_time,
                            login_status="logged_in"
                        )
                    
                    # 方法2：如果Cookie切换失败，尝试重新登录
                    if self._switch_by_relogin(browser_id, driver, target_account):
                        switch_time = time.time() - start_time
                        browser_state.current_account = target_account
                        browser_state.last_switch_time = time.time()
                        browser_state.switch_count += 1
                        
                        self.logger.info(f"✅ 浏览器{browser_id}重新登录切换成功：{target_account.email}，耗时{switch_time:.2f}秒")
                        
                        return AccountSwitchResult(
                            success=True,
                            account_email=target_account.email,
                            switch_time=switch_time,
                            login_status="logged_in"
                        )
                    
                    self.logger.warning(f"⚠️ 浏览器{browser_id}账号切换尝试{attempt+1}失败")
                    time.sleep(self.switch_interval)
                    
                except Exception as e:
                    self.logger.error(f"浏览器{browser_id}账号切换尝试{attempt+1}异常: {e}")
                    time.sleep(self.switch_interval)
            
            # 所有尝试都失败
            browser_state.failed_switches += 1
            switch_time = time.time() - start_time
            
            return AccountSwitchResult(
                success=False,
                account_email=target_account.email,
                switch_time=switch_time,
                error_message="所有切换尝试都失败",
                login_status="error"
            )
            
        except Exception as e:
            switch_time = time.time() - start_time
            self.logger.error(f"浏览器{browser_id}账号切换异常: {e}")
            
            return AccountSwitchResult(
                success=False,
                account_email=target_account.email,
                switch_time=switch_time,
                error_message=str(e),
                login_status="error"
            )
    
    def _preload_account_cookies(self, browser_id: int, account: Account) -> bool:
        """预加载账号Cookie"""
        try:
            # 从Cookie管理器获取账号Cookie
            cookies = self.cookie_manager.get_account_cookies(account.email)
            if cookies:
                browser_state = self.browser_states[browser_id]
                browser_state.account_cookies[account.email] = cookies
                self.logger.debug(f"预加载账号{account.email}的Cookie成功")
                return True
            else:
                self.logger.warning(f"账号{account.email}没有可用的Cookie")
                return False
                
        except Exception as e:
            self.logger.error(f"预加载账号{account.email}的Cookie失败: {e}")
            return False
    
    def _switch_by_cookies(
        self,
        browser_id: int,
        driver: webdriver.Chrome,
        target_account: Account
    ) -> bool:
        """通过Cookie切换账号"""
        try:
            browser_state = self.browser_states[browser_id]
            
            # 检查是否有预加载的Cookie
            if target_account.email not in browser_state.account_cookies:
                self.logger.warning(f"账号{target_account.email}没有预加载的Cookie")
                return False
            
            cookies = browser_state.account_cookies[target_account.email]
            
            # 清除当前Cookie
            driver.delete_all_cookies()
            
            # 导航到新浪邮箱
            driver.get(self.sina_login_url)
            time.sleep(2)
            
            # 设置新Cookie
            for cookie in cookies:
                try:
                    driver.add_cookie(cookie)
                except Exception as e:
                    self.logger.debug(f"设置Cookie失败: {e}")
            
            # 刷新页面验证登录状态
            driver.refresh()
            time.sleep(3)
            
            # 检查是否登录成功
            if self._verify_login_status(driver, target_account.email):
                self.logger.info(f"Cookie切换成功：{target_account.email}")
                return True
            else:
                self.logger.warning(f"Cookie切换后验证失败：{target_account.email}")
                return False
                
        except Exception as e:
            self.logger.error(f"Cookie切换失败: {e}")
            return False
    
    def _switch_by_relogin(
        self,
        browser_id: int,
        driver: webdriver.Chrome,
        target_account: Account
    ) -> bool:
        """通过重新登录切换账号"""
        try:
            # 清除所有Cookie
            driver.delete_all_cookies()
            
            # 导航到登录页面
            driver.get(self.sina_login_url)
            time.sleep(3)
            
            # 执行登录
            if self._perform_login(driver, target_account):
                # 保存新的Cookie
                self._save_account_cookies(browser_id, target_account, driver)
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"重新登录切换失败: {e}")
            return False
    
    def _perform_login(self, driver: webdriver.Chrome, account: Account) -> bool:
        """执行登录操作"""
        try:
            wait = WebDriverWait(driver, self.switch_timeout)
            
            # 查找用户名输入框
            username_input = wait.until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            username_input.clear()
            username_input.send_keys(account.email)
            
            # 查找密码输入框
            password_input = driver.find_element(By.NAME, "password")
            password_input.clear()
            password_input.send_keys(account.password)
            
            # 点击登录按钮
            login_button = driver.find_element(By.XPATH, "//input[@type='submit' or @value='登录']")
            login_button.click()
            
            # 等待登录完成
            time.sleep(5)
            
            # 验证登录状态
            return self._verify_login_status(driver, account.email)
            
        except Exception as e:
            self.logger.error(f"执行登录操作失败: {e}")
            return False
    
    def _verify_login_status(self, driver: webdriver.Chrome, account_email: str) -> bool:
        """验证登录状态"""
        try:
            # 检查是否存在登录成功的标识
            # 这里可以根据新浪邮箱的具体页面结构来判断
            
            # 方法1：检查URL是否包含邮箱主页特征
            current_url = driver.current_url
            if "mail.sina.com.cn" in current_url and "login" not in current_url:
                return True
            
            # 方法2：检查页面是否包含用户信息
            try:
                # 查找用户邮箱地址或用户名显示
                user_elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{account_email}')]")
                if user_elements:
                    return True
            except:
                pass
            
            # 方法3：检查是否能访问写信页面
            try:
                driver.get(self.sina_compose_url)
                time.sleep(2)
                
                # 检查是否有写信相关元素
                compose_elements = driver.find_elements(By.XPATH, "//input[@name='to'] | //textarea[@name='content']")
                if compose_elements:
                    return True
            except:
                pass
            
            return False
            
        except Exception as e:
            self.logger.error(f"验证登录状态失败: {e}")
            return False
    
    def _save_account_cookies(
        self,
        browser_id: int,
        account: Account,
        driver: webdriver.Chrome
    ):
        """保存账号Cookie"""
        try:
            cookies = driver.get_cookies()
            
            # 保存到本地状态
            browser_state = self.browser_states[browser_id]
            browser_state.account_cookies[account.email] = cookies
            
            # 保存到Cookie管理器
            self.cookie_manager.save_account_cookies(account.email, cookies)
            
            self.logger.debug(f"保存账号{account.email}的Cookie成功")
            
        except Exception as e:
            self.logger.error(f"保存账号{account.email}的Cookie失败: {e}")
    
    def get_current_account(self, browser_id: int) -> Optional[Account]:
        """获取当前账号"""
        if browser_id in self.browser_states:
            return self.browser_states[browser_id].current_account
        return None
    
    def get_browser_stats(self, browser_id: int) -> Dict[str, Any]:
        """获取浏览器统计信息"""
        if browser_id not in self.browser_states:
            return {}
        
        state = self.browser_states[browser_id]
        return {
            'browser_id': browser_id,
            'current_account': state.current_account.email if state.current_account else None,
            'available_accounts_count': len(state.available_accounts),
            'switch_count': state.switch_count,
            'failed_switches': state.failed_switches,
            'last_switch_time': state.last_switch_time,
            'cached_cookies_count': len(state.account_cookies)
        }
