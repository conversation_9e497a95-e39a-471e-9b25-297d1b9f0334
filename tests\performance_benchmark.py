#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多浏览器并发发送系统性能基准测试
测试系统在不同负载下的性能表现
"""

import time
import statistics
import psutil
import gc
from typing import List, Dict, Any
from dataclasses import dataclass

# 添加项目根目录到路径
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.models.account import Account
from src.core.smart_account_strategy import SmartAccountStrategy, SendingMode
from src.core.intelligent_task_distributor import IntelligentTaskDistributor
from src.core.multi_browser_integration_adapter import MultiBrowserIntegrationAdapter, IntegrationConfig


@dataclass
class BenchmarkResult:
    """基准测试结果"""
    test_name: str
    accounts_count: int
    recipients_count: int
    browser_count: int
    execution_time: float
    memory_usage: float
    cpu_usage: float
    tasks_per_second: float
    success: bool
    error_message: str = ""


class PerformanceBenchmark:
    """性能基准测试"""
    
    def __init__(self):
        self.results: List[BenchmarkResult] = []
        self.process = psutil.Process()
    
    def create_test_accounts(self, count: int) -> List[Account]:
        """创建测试账号"""
        accounts = []
        for i in range(count):
            account = Account(
                email=f"test{i+1}@sina.com",
                password=f"password{i+1}",
                smtp_server="smtp.sina.com",
                smtp_port=587,
                imap_server="imap.sina.com",
                imap_port=993
            )
            accounts.append(account)
        return accounts
    
    def create_test_recipients(self, count: int) -> List[str]:
        """创建测试收件人"""
        return [f"recipient{i+1}@example.com" for i in range(count)]
    
    def measure_performance(self, func, *args, **kwargs) -> tuple:
        """测量函数执行性能"""
        # 垃圾回收
        gc.collect()
        
        # 记录初始状态
        initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        initial_cpu = self.process.cpu_percent()
        
        # 执行函数
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            success = True
            error_msg = ""
        except Exception as e:
            result = None
            success = False
            error_msg = str(e)
        
        end_time = time.time()
        
        # 记录最终状态
        final_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        final_cpu = self.process.cpu_percent()
        
        execution_time = end_time - start_time
        memory_usage = final_memory - initial_memory
        cpu_usage = (initial_cpu + final_cpu) / 2
        
        return result, execution_time, memory_usage, cpu_usage, success, error_msg
    
    def benchmark_strategy_analysis(self):
        """基准测试策略分析性能"""
        print("📊 基准测试：策略分析性能")
        
        test_cases = [
            (1, 10, 1),    # 小规模
            (3, 50, 3),    # 中规模
            (5, 100, 5),   # 大规模
            (10, 500, 10), # 超大规模
        ]
        
        strategy_analyzer = SmartAccountStrategy()
        
        for accounts_count, recipients_count, browser_count in test_cases:
            accounts = self.create_test_accounts(accounts_count)
            recipients = self.create_test_recipients(recipients_count)
            
            def analyze_strategy():
                return strategy_analyzer.analyze_sending_strategy(
                    available_accounts=accounts,
                    recipient_emails=recipients,
                    sending_mode=SendingMode.INDIVIDUAL,
                    user_browser_count=browser_count
                )
            
            result, exec_time, memory, cpu, success, error = self.measure_performance(analyze_strategy)
            
            tasks_per_second = recipients_count / exec_time if exec_time > 0 else 0
            
            benchmark_result = BenchmarkResult(
                test_name=f"策略分析_{accounts_count}账号_{recipients_count}收件人",
                accounts_count=accounts_count,
                recipients_count=recipients_count,
                browser_count=browser_count,
                execution_time=exec_time,
                memory_usage=memory,
                cpu_usage=cpu,
                tasks_per_second=tasks_per_second,
                success=success,
                error_message=error
            )
            
            self.results.append(benchmark_result)
            
            print(f"  ✅ {benchmark_result.test_name}: {exec_time:.3f}s, {memory:.1f}MB, {tasks_per_second:.1f} tasks/s")
    
    def benchmark_task_distribution(self):
        """基准测试任务分配性能"""
        print("📊 基准测试：任务分配性能")
        
        test_cases = [
            (2, 20, 2),
            (3, 100, 3),
            (5, 500, 5),
            (10, 1000, 10),
        ]
        
        for accounts_count, recipients_count, browser_count in test_cases:
            accounts = self.create_test_accounts(accounts_count)
            recipients = self.create_test_recipients(recipients_count)
            
            # 先创建策略
            strategy_analyzer = SmartAccountStrategy()
            strategy = strategy_analyzer.analyze_sending_strategy(
                available_accounts=accounts,
                recipient_emails=recipients,
                sending_mode=SendingMode.INDIVIDUAL,
                user_browser_count=browser_count
            )
            
            def distribute_tasks():
                distributor = IntelligentTaskDistributor()
                return distributor.distribute_tasks(
                    strategy=strategy,
                    recipient_emails=recipients,
                    subject="测试邮件",
                    content="测试内容",
                    sending_mode=SendingMode.INDIVIDUAL
                )
            
            result, exec_time, memory, cpu, success, error = self.measure_performance(distribute_tasks)
            
            tasks_per_second = recipients_count / exec_time if exec_time > 0 else 0
            
            benchmark_result = BenchmarkResult(
                test_name=f"任务分配_{accounts_count}账号_{recipients_count}任务",
                accounts_count=accounts_count,
                recipients_count=recipients_count,
                browser_count=browser_count,
                execution_time=exec_time,
                memory_usage=memory,
                cpu_usage=cpu,
                tasks_per_second=tasks_per_second,
                success=success,
                error_message=error
            )
            
            self.results.append(benchmark_result)
            
            print(f"  ✅ {benchmark_result.test_name}: {exec_time:.3f}s, {memory:.1f}MB, {tasks_per_second:.1f} tasks/s")
    
    def benchmark_integration_adapter(self):
        """基准测试集成适配器性能"""
        print("📊 基准测试：集成适配器性能")
        
        test_cases = [
            (3, 50),
            (5, 200),
            (10, 1000),
        ]
        
        for accounts_count, recipients_count in test_cases:
            accounts = self.create_test_accounts(accounts_count)
            recipients = self.create_test_recipients(recipients_count)
            
            def test_adapter():
                config = IntegrationConfig(
                    enable_concurrent_mode=True,
                    max_browsers=accounts_count,
                    send_interval=0.1
                )
                
                adapter = MultiBrowserIntegrationAdapter(config)
                return adapter.analyze_sending_requirements(
                    accounts=accounts,
                    recipient_emails=recipients,
                    sending_mode="individual"
                )
            
            result, exec_time, memory, cpu, success, error = self.measure_performance(test_adapter)
            
            tasks_per_second = recipients_count / exec_time if exec_time > 0 else 0
            
            benchmark_result = BenchmarkResult(
                test_name=f"集成适配器_{accounts_count}账号_{recipients_count}收件人",
                accounts_count=accounts_count,
                recipients_count=recipients_count,
                browser_count=accounts_count,
                execution_time=exec_time,
                memory_usage=memory,
                cpu_usage=cpu,
                tasks_per_second=tasks_per_second,
                success=success,
                error_message=error
            )
            
            self.results.append(benchmark_result)
            
            print(f"  ✅ {benchmark_result.test_name}: {exec_time:.3f}s, {memory:.1f}MB, {tasks_per_second:.1f} tasks/s")
    
    def benchmark_memory_scaling(self):
        """基准测试内存扩展性"""
        print("📊 基准测试：内存扩展性")
        
        # 测试不同规模下的内存使用
        scales = [10, 50, 100, 500, 1000]
        
        for scale in scales:
            accounts = self.create_test_accounts(min(scale // 10, 20))
            recipients = self.create_test_recipients(scale)
            
            def memory_test():
                strategy_analyzer = SmartAccountStrategy()
                distributor = IntelligentTaskDistributor()
                
                # 执行多次操作
                for _ in range(5):
                    strategy = strategy_analyzer.analyze_sending_strategy(
                        available_accounts=accounts,
                        recipient_emails=recipients,
                        sending_mode=SendingMode.INDIVIDUAL
                    )
                    
                    distributor.distribute_tasks(
                        strategy=strategy,
                        recipient_emails=recipients,
                        subject="测试",
                        content="测试",
                        sending_mode=SendingMode.INDIVIDUAL
                    )
                
                return True
            
            result, exec_time, memory, cpu, success, error = self.measure_performance(memory_test)
            
            benchmark_result = BenchmarkResult(
                test_name=f"内存扩展_{scale}收件人",
                accounts_count=len(accounts),
                recipients_count=scale,
                browser_count=len(accounts),
                execution_time=exec_time,
                memory_usage=memory,
                cpu_usage=cpu,
                tasks_per_second=scale / exec_time if exec_time > 0 else 0,
                success=success,
                error_message=error
            )
            
            self.results.append(benchmark_result)
            
            print(f"  ✅ {benchmark_result.test_name}: {memory:.1f}MB, {exec_time:.3f}s")
    
    def run_all_benchmarks(self):
        """运行所有基准测试"""
        print("🚀 开始性能基准测试...")
        print("=" * 60)
        
        start_time = time.time()
        
        self.benchmark_strategy_analysis()
        print()
        
        self.benchmark_task_distribution()
        print()
        
        self.benchmark_integration_adapter()
        print()
        
        self.benchmark_memory_scaling()
        print()
        
        total_time = time.time() - start_time
        
        print("=" * 60)
        print(f"📈 基准测试完成，总耗时: {total_time:.2f}秒")
        
        self.generate_report()
    
    def generate_report(self):
        """生成性能报告"""
        print("\n📋 性能基准测试报告")
        print("=" * 60)
        
        # 按测试类型分组
        by_type = {}
        for result in self.results:
            test_type = result.test_name.split('_')[0]
            if test_type not in by_type:
                by_type[test_type] = []
            by_type[test_type].append(result)
        
        for test_type, results in by_type.items():
            print(f"\n🔍 {test_type}测试结果:")
            
            exec_times = [r.execution_time for r in results if r.success]
            memory_usages = [r.memory_usage for r in results if r.success]
            tasks_per_sec = [r.tasks_per_second for r in results if r.success]
            
            if exec_times:
                print(f"  执行时间: 平均 {statistics.mean(exec_times):.3f}s, 最大 {max(exec_times):.3f}s")
                print(f"  内存使用: 平均 {statistics.mean(memory_usages):.1f}MB, 最大 {max(memory_usages):.1f}MB")
                print(f"  处理速度: 平均 {statistics.mean(tasks_per_sec):.1f} tasks/s, 最大 {max(tasks_per_sec):.1f} tasks/s")
            
            failed_count = len([r for r in results if not r.success])
            if failed_count > 0:
                print(f"  ❌ 失败测试: {failed_count}/{len(results)}")
        
        # 总体统计
        all_successful = [r for r in self.results if r.success]
        if all_successful:
            print(f"\n📊 总体性能指标:")
            print(f"  成功率: {len(all_successful)}/{len(self.results)} ({len(all_successful)/len(self.results)*100:.1f}%)")
            print(f"  平均执行时间: {statistics.mean([r.execution_time for r in all_successful]):.3f}s")
            print(f"  平均内存使用: {statistics.mean([r.memory_usage for r in all_successful]):.1f}MB")
            print(f"  平均处理速度: {statistics.mean([r.tasks_per_second for r in all_successful]):.1f} tasks/s")


def run_performance_benchmark():
    """运行性能基准测试"""
    benchmark = PerformanceBenchmark()
    benchmark.run_all_benchmarks()
    return benchmark.results


if __name__ == "__main__":
    run_performance_benchmark()
