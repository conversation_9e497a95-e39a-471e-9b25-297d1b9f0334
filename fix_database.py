#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库修复脚本
修复数据库中缺失的字段和索引问题
"""

import sqlite3
import os
from pathlib import Path

def fix_database():
    """修复数据库"""
    print("🔧 开始修复数据库...")
    
    # 数据库路径
    db_path = Path("data/sina_email.db")
    
    if not db_path.exists():
        print("❌ 数据库文件不存在，无需修复")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        print("📊 检查数据库结构...")
        
        # 检查accounts表结构
        cursor.execute("PRAGMA table_info(accounts)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"当前accounts表字段: {columns}")
        
        # 添加缺失的字段
        fields_to_add = [
            ('cooling_until', 'DATETIME'),
            ('cooling_reason', 'TEXT')
        ]
        
        for field_name, field_type in fields_to_add:
            if field_name not in columns:
                try:
                    cursor.execute(f"ALTER TABLE accounts ADD COLUMN {field_name} {field_type}")
                    print(f"✅ 已添加字段: {field_name}")
                except sqlite3.OperationalError as e:
                    print(f"⚠️ 添加字段 {field_name} 失败: {e}")
        
        # 创建索引
        indexes_to_create = [
            ("idx_accounts_email", "accounts(email)"),
            ("idx_accounts_status", "accounts(status)"),
            ("idx_accounts_cooling", "accounts(cooling_until)"),
        ]
        
        for index_name, index_def in indexes_to_create:
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON {index_def}")
                print(f"✅ 已创建索引: {index_name}")
            except sqlite3.OperationalError as e:
                print(f"⚠️ 创建索引 {index_name} 失败: {e}")
        
        # 提交更改
        conn.commit()
        print("✅ 数据库修复完成")
        
        # 验证修复结果
        cursor.execute("PRAGMA table_info(accounts)")
        new_columns = [column[1] for column in cursor.fetchall()]
        print(f"修复后accounts表字段: {new_columns}")
        
        # 检查索引
        cursor.execute("PRAGMA index_list(accounts)")
        indexes = [index[1] for index in cursor.fetchall()]
        print(f"当前索引: {indexes}")
        
    except Exception as e:
        print(f"❌ 数据库修复失败: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

def test_concurrent_system():
    """测试多浏览器并发发送系统"""
    print("\n🧪 测试多浏览器并发发送系统...")
    
    try:
        # 测试导入
        from src.core.multi_browser_integration_adapter import MultiBrowserIntegrationAdapter, IntegrationConfig
        from src.core.smart_account_strategy import SmartAccountStrategy, SendingMode
        from src.core.intelligent_task_distributor import IntelligentTaskDistributor
        from src.core.enhanced_cookie_account_switcher import EnhancedCookieAccountSwitcher
        from src.core.concurrent_email_sender_manager import ConcurrentEmailSenderManager, ConcurrentSendingConfig
        from src.models.account import Account
        
        print("✅ 所有核心组件导入成功")
        
        # 测试策略分析
        strategy_analyzer = SmartAccountStrategy()
        
        # 创建测试账号
        test_accounts = [
            Account(
                email="<EMAIL>",
                password="password1",
                status="active"
            ),
            Account(
                email="<EMAIL>",
                password="password2",
                status="active"
            ),
            Account(
                email="<EMAIL>",
                password="password3",
                status="active"
            )
        ]
        
        # 创建测试收件人
        test_recipients = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        # 测试策略分析
        strategy = strategy_analyzer.analyze_sending_strategy(
            available_accounts=test_accounts,
            recipient_emails=test_recipients,
            sending_mode=SendingMode.INDIVIDUAL,
            user_browser_count=3
        )
        
        print(f"✅ 策略分析成功:")
        print(f"   - 浏览器模式: {strategy.browser_mode.value}")
        print(f"   - 浏览器数量: {strategy.browser_count}")
        print(f"   - 每账号发送: {strategy.emails_per_account}")
        print(f"   - 预估时间: {strategy.estimated_time:.1f}分钟")
        
        # 测试任务分配
        distributor = IntelligentTaskDistributor()
        browser_queues = distributor.distribute_tasks(
            strategy=strategy,
            recipient_emails=test_recipients,
            subject="测试邮件",
            content="这是一封测试邮件",
            sending_mode=SendingMode.INDIVIDUAL
        )
        
        total_tasks = sum(len(queue.task_queue) for queue in browser_queues.values())
        print(f"✅ 任务分配成功: {total_tasks}个任务分配到{len(browser_queues)}个浏览器")
        
        # 测试集成适配器
        config = IntegrationConfig(
            enable_concurrent_mode=True,
            max_browsers=3,
            send_interval=2.0
        )
        
        adapter = MultiBrowserIntegrationAdapter(config)
        analysis = adapter.analyze_sending_requirements(
            accounts=test_accounts,
            recipient_emails=test_recipients,
            user_browser_count=3,
            sending_mode="individual"
        )
        
        print(f"✅ 集成适配器测试成功")
        print(f"   - 推荐浏览器数: {analysis['strategy']['browser_count']}")
        print(f"   - 总邮件数: {analysis['strategy']['total_emails']}")
        
        print("\n🎉 多浏览器并发发送系统测试完成，所有功能正常！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 新浪邮箱多浏览器并发发送系统修复和测试")
    print("=" * 60)
    
    # 修复数据库
    fix_database()
    
    # 测试并发系统
    test_result = test_concurrent_system()
    
    print("\n" + "=" * 60)
    if test_result:
        print("🎉 系统修复和测试完成，可以正常启动程序！")
        print("\n📋 多浏览器并发发送功能说明:")
        print("1. 在'多浏览器发送'标签页启用'智能并发模式'")
        print("2. 设置'同时账号数'大于1（推荐3-5个）")
        print("3. 系统会自动判断使用多浏览器并发模式")
        print("4. 每个浏览器独立线程，支持智能账号切换")
        print("5. 根据收件人数量智能分配任务到各浏览器")
    else:
        print("❌ 系统测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
