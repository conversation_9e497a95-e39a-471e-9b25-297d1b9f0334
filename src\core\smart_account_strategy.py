#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能账号策略管理器
根据账号数量、收件人数量、发送模式等因素智能判断使用单浏览器还是多浏览器模式
"""

import math
from typing import List, Dict, Any, Tuple, Optional
from enum import Enum
from dataclasses import dataclass
from loguru import logger

from src.models.account import Account


class BrowserMode(Enum):
    """浏览器模式枚举"""
    SINGLE = "single"           # 单浏览器模式
    MULTI = "multi"            # 多浏览器模式


class SendingMode(Enum):
    """发送模式枚举"""
    INDIVIDUAL = "individual"   # 单独发送（一封一个收件人）
    BATCH = "batch"            # 批量发送（一封多个收件人）


@dataclass
class AccountStrategy:
    """账号策略配置"""
    browser_mode: BrowserMode
    browser_count: int
    accounts_per_browser: List[List[Account]]  # 每个浏览器分配的账号列表
    emails_per_account: int                    # 每个账号发送的邮件数量
    total_emails: int                         # 总邮件数量
    switch_threshold: int                     # 账号切换阈值
    estimated_time: float                     # 预估完成时间（分钟）
    
    def get_browser_allocation(self) -> Dict[int, Dict[str, Any]]:
        """获取浏览器分配详情"""
        allocation = {}
        for i, accounts in enumerate(self.accounts_per_browser):
            allocation[i] = {
                'browser_id': i,
                'accounts': [acc.email for acc in accounts],
                'account_count': len(accounts),
                'emails_per_account': self.emails_per_account,
                'total_emails_for_browser': len(accounts) * self.emails_per_account
            }
        return allocation


class SmartAccountStrategy:
    """智能账号策略管理器"""
    
    def __init__(self):
        self.logger = logger
        
        # 策略配置参数
        self.single_browser_threshold = 1      # 单浏览器阈值
        self.max_browsers = 10                 # 最大浏览器数量
        self.emails_per_account_default = 10   # 默认每账号发送数量
        self.send_interval = 2.0               # 发送间隔（秒）
        self.account_switch_overhead = 30      # 账号切换开销（秒）
    
    def analyze_sending_strategy(
        self,
        available_accounts: List[Account],
        recipient_emails: List[str],
        sending_mode: SendingMode,
        user_browser_count: Optional[int] = None,
        emails_per_account: Optional[int] = None
    ) -> AccountStrategy:
        """
        分析发送策略
        
        Args:
            available_accounts: 可用账号列表
            recipient_emails: 收件人邮箱列表
            sending_mode: 发送模式
            user_browser_count: 用户指定的浏览器数量
            emails_per_account: 每账号发送数量
            
        Returns:
            AccountStrategy: 智能分析后的账号策略
        """
        try:
            # 基础参数
            total_accounts = len(available_accounts)
            total_recipients = len(recipient_emails)
            emails_per_account = emails_per_account or self.emails_per_account_default
            
            self.logger.info(f"📊 策略分析开始：")
            self.logger.info(f"   - 可用账号数：{total_accounts}")
            self.logger.info(f"   - 收件人数：{total_recipients}")
            self.logger.info(f"   - 发送模式：{sending_mode.value}")
            self.logger.info(f"   - 每账号发送数：{emails_per_account}")
            
            # 计算实际需要发送的邮件数量
            if sending_mode == SendingMode.INDIVIDUAL:
                # 单独发送：每个收件人一封邮件
                total_emails = total_recipients
            else:
                # 批量发送：根据批量大小计算
                total_emails = total_recipients  # 简化处理，实际可能更复杂
            
            # 判断浏览器模式
            browser_mode, optimal_browser_count = self._determine_browser_mode(
                total_accounts, total_emails, user_browser_count
            )
            
            # 分配账号到浏览器
            accounts_per_browser = self._allocate_accounts_to_browsers(
                available_accounts, optimal_browser_count
            )
            
            # 计算每个账号的发送数量
            actual_emails_per_account = self._calculate_emails_per_account(
                total_emails, total_accounts, emails_per_account
            )
            
            # 计算账号切换阈值
            switch_threshold = min(emails_per_account, actual_emails_per_account)
            
            # 预估完成时间
            estimated_time = self._estimate_completion_time(
                total_emails, optimal_browser_count, self.send_interval
            )
            
            strategy = AccountStrategy(
                browser_mode=browser_mode,
                browser_count=optimal_browser_count,
                accounts_per_browser=accounts_per_browser,
                emails_per_account=actual_emails_per_account,
                total_emails=total_emails,
                switch_threshold=switch_threshold,
                estimated_time=estimated_time
            )
            
            self._log_strategy_summary(strategy)
            return strategy
            
        except Exception as e:
            self.logger.error(f"策略分析失败: {e}")
            # 返回默认策略
            return self._get_default_strategy(available_accounts, recipient_emails)
    
    def _determine_browser_mode(
        self,
        total_accounts: int,
        total_emails: int,
        user_browser_count: Optional[int]
    ) -> Tuple[BrowserMode, int]:
        """确定浏览器模式和数量"""
        
        # 如果用户指定了浏览器数量
        if user_browser_count is not None:
            if user_browser_count <= self.single_browser_threshold:
                return BrowserMode.SINGLE, 1
            else:
                optimal_count = min(user_browser_count, self.max_browsers, total_accounts)
                return BrowserMode.MULTI, optimal_count
        
        # 自动判断
        if total_accounts <= self.single_browser_threshold:
            return BrowserMode.SINGLE, 1
        
        # 多浏览器模式：根据账号数量和邮件数量计算最优浏览器数量
        # 考虑因素：
        # 1. 每个浏览器至少要有一个账号
        # 2. 不超过最大浏览器数量
        # 3. 考虑并发效率
        
        optimal_count = min(
            total_accounts,                    # 不超过账号数量
            self.max_browsers,                 # 不超过最大限制
            max(2, math.ceil(total_emails / 100))  # 根据邮件数量动态调整
        )
        
        return BrowserMode.MULTI, optimal_count
    
    def _allocate_accounts_to_browsers(
        self,
        accounts: List[Account],
        browser_count: int
    ) -> List[List[Account]]:
        """将账号分配到浏览器"""
        
        if browser_count == 1:
            return [accounts]
        
        # 平均分配账号
        accounts_per_browser = len(accounts) // browser_count
        remainder = len(accounts) % browser_count
        
        allocation = []
        start_idx = 0
        
        for i in range(browser_count):
            # 前面的浏览器多分配一个账号（如果有余数）
            count = accounts_per_browser + (1 if i < remainder else 0)
            end_idx = start_idx + count
            
            browser_accounts = accounts[start_idx:end_idx]
            allocation.append(browser_accounts)
            
            start_idx = end_idx
        
        return allocation
    
    def _calculate_emails_per_account(
        self,
        total_emails: int,
        total_accounts: int,
        preferred_emails_per_account: int
    ) -> int:
        """计算每个账号实际发送的邮件数量"""
        
        if total_accounts == 0:
            return 0
        
        # 平均分配
        avg_emails = math.ceil(total_emails / total_accounts)
        
        # 不超过用户偏好设置
        return min(avg_emails, preferred_emails_per_account)
    
    def _estimate_completion_time(
        self,
        total_emails: int,
        browser_count: int,
        send_interval: float
    ) -> float:
        """预估完成时间（分钟）"""
        
        if browser_count == 0:
            return 0
        
        # 并发发送时间 = 总邮件数 / 浏览器数量 * 发送间隔
        parallel_time = (total_emails / browser_count) * send_interval
        
        # 加上账号切换开销
        switch_overhead = (total_emails / self.emails_per_account_default) * self.account_switch_overhead
        
        total_seconds = parallel_time + switch_overhead
        return total_seconds / 60  # 转换为分钟
    
    def _log_strategy_summary(self, strategy: AccountStrategy):
        """记录策略摘要"""
        self.logger.info(f"🎯 策略分析结果：")
        self.logger.info(f"   - 浏览器模式：{strategy.browser_mode.value}")
        self.logger.info(f"   - 浏览器数量：{strategy.browser_count}")
        self.logger.info(f"   - 每账号发送：{strategy.emails_per_account} 封")
        self.logger.info(f"   - 总邮件数：{strategy.total_emails}")
        self.logger.info(f"   - 预估时间：{strategy.estimated_time:.1f} 分钟")
        
        # 详细分配信息
        allocation = strategy.get_browser_allocation()
        for browser_id, info in allocation.items():
            self.logger.info(f"   - 浏览器{browser_id+1}：{info['account_count']}个账号，{info['total_emails_for_browser']}封邮件")
    
    def _get_default_strategy(
        self,
        accounts: List[Account],
        recipients: List[str]
    ) -> AccountStrategy:
        """获取默认策略"""
        return AccountStrategy(
            browser_mode=BrowserMode.SINGLE,
            browser_count=1,
            accounts_per_browser=[accounts],
            emails_per_account=min(len(recipients), self.emails_per_account_default),
            total_emails=len(recipients),
            switch_threshold=self.emails_per_account_default,
            estimated_time=len(recipients) * self.send_interval / 60
        )
