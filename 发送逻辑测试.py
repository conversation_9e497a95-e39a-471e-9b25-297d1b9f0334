#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发送逻辑测试脚本
测试修复后的发送逻辑是否正确工作
"""

import sys
import os
sys.path.insert(0, '.')

def test_task_queue_status():
    """测试任务队列状态"""
    print("🔧 测试任务队列状态...")
    
    try:
        from src.core.smart_task_queue import SmartTaskQueue
        from src.core.email_sending_manager import EmailSendingManager
        from src.core.batch_processor import BatchProcessor
        from src.models.database import DatabaseManager
        from src.utils.config_manager import ConfigManager
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get('database.path', 'data/sina_email.db')
        db_manager = DatabaseManager(db_path)
        
        # 创建任务队列配置
        from src.core.smart_task_queue import QueueConfig
        from src.core.batch_processor import BatchConfig
        from src.core.email_sending_manager import SendingConfig
        queue_config = QueueConfig()
        batch_config = BatchConfig()
        sending_config = SendingConfig()

        # 创建任务队列
        task_queue = SmartTaskQueue(queue_config, db_path)
        batch_processor = BatchProcessor(task_queue, batch_config)
        task_sending_manager = EmailSendingManager(sending_config)
        
        # 获取任务状态
        status = task_sending_manager.get_sending_status()
        
        print("📊 任务队列状态:")
        print(f"   📈 总任务数: {status['queue_stats']['total_tasks']}")
        print(f"   ⏳ 待发送: {status['queue_stats'].get('pending_tasks', 0)}")
        print(f"   ✅ 已完成: {status['queue_stats'].get('completed_tasks', 0)}")
        print(f"   ❌ 失败: {status['queue_stats'].get('failed_tasks', 0)}")
        print(f"   🔄 发送中: {status['queue_stats'].get('processing_tasks', 0)}")
        
        # 检查是否有任务
        has_tasks = status['queue_stats']['total_tasks'] > 0
        print(f"\n🎯 任务检查结果: {'有任务' if has_tasks else '无任务'}")
        
        return has_tasks, status
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False, None

def test_recipient_validation_logic():
    """测试收件人验证逻辑"""
    print("\n🔧 测试收件人验证逻辑...")
    
    try:
        # 模拟不同的收件人情况
        test_cases = [
            {
                "name": "手动输入模式 - 有收件人",
                "manual_mode": True,
                "recipients_text": "<EMAIL>\<EMAIL>",
                "has_existing_tasks": False,
                "expected_result": True
            },
            {
                "name": "手动输入模式 - 无收件人",
                "manual_mode": True,
                "recipients_text": "",
                "has_existing_tasks": False,
                "expected_result": False
            },
            {
                "name": "数据源模式 - 有现有任务",
                "manual_mode": False,
                "recipients_text": "",
                "has_existing_tasks": True,
                "expected_result": True
            },
            {
                "name": "数据源模式 - 无现有任务",
                "manual_mode": False,
                "recipients_text": "",
                "has_existing_tasks": False,
                "expected_result": False
            }
        ]
        
        print("📋 验证逻辑测试:")
        
        for test_case in test_cases:
            # 模拟验证逻辑
            recipients = []
            
            if test_case["manual_mode"]:
                # 手动输入模式
                if test_case["recipients_text"]:
                    recipients = [email.strip() for email in test_case["recipients_text"].split('\n') if email.strip()]
            else:
                # 数据源模式
                if test_case["has_existing_tasks"]:
                    recipients = ["existing_tasks"]  # 标记有现有任务
            
            # 检查收件人
            if not recipients:
                if test_case["has_existing_tasks"]:
                    recipients = ["existing_tasks"]
            
            # 验证结果
            has_recipients = len(recipients) > 0
            result_match = has_recipients == test_case["expected_result"]
            
            status = "✅" if result_match else "❌"
            print(f"   {status} {test_case['name']}: {'通过' if result_match else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_concurrent_sending_logic():
    """测试并发发送逻辑"""
    print("\n🔧 测试并发发送逻辑...")
    
    try:
        # 检查关键方法是否存在
        from src.gui.optimized_multi_browser_widget import OptimizedMultiBrowserWidget
        import inspect
        
        # 检查新增的方法
        methods_to_check = [
            "_start_concurrent_with_existing_tasks",
            "_start_concurrent_sending"
        ]
        
        print("📋 并发发送方法检查:")
        
        for method_name in methods_to_check:
            exists = hasattr(OptimizedMultiBrowserWidget, method_name)
            status = "✅" if exists else "❌"
            print(f"   {status} {method_name}: {'存在' if exists else '不存在'}")
            
            if exists:
                # 检查方法实现
                method = getattr(OptimizedMultiBrowserWidget, method_name)
                source = inspect.getsource(method)
                has_implementation = len(source.strip().split('\n')) > 3
                impl_status = "✅" if has_implementation else "⚠️"
                print(f"      {impl_status} 实现状态: {'有实现' if has_implementation else '仅声明'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_email_sending_manager():
    """测试邮件发送管理器"""
    print("\n🔧 测试邮件发送管理器...")
    
    try:
        from src.core.email_sending_manager import EmailSendingManager
        from src.core.smart_task_queue import SmartTaskQueue
        from src.core.batch_processor import BatchProcessor
        from src.models.database import DatabaseManager
        from src.utils.config_manager import ConfigManager
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get('database.path', 'data/sina_email.db')
        db_manager = DatabaseManager(db_path)
        
        # 创建任务队列配置
        from src.core.smart_task_queue import QueueConfig
        from src.core.batch_processor import BatchConfig
        from src.core.email_sending_manager import SendingConfig
        queue_config = QueueConfig()
        batch_config = BatchConfig()
        sending_config = SendingConfig()

        task_queue = SmartTaskQueue(queue_config, db_path)
        batch_processor = BatchProcessor(task_queue, batch_config)
        manager = EmailSendingManager(sending_config)
        
        # 测试关键方法
        methods_to_test = [
            "get_sending_status",
            "start_sending",
            "stop_sending",
            "pause_sending",
            "resume_sending"
        ]
        
        print("📋 邮件发送管理器方法检查:")
        
        for method_name in methods_to_test:
            exists = hasattr(manager, method_name)
            status = "✅" if exists else "❌"
            print(f"   {status} {method_name}: {'存在' if exists else '不存在'}")
        
        # 测试获取状态
        try:
            status = manager.get_sending_status()
            print(f"\n📊 发送状态获取: ✅ 成功")
            print(f"   📈 总任务数: {status['queue_stats']['total_tasks']}")
        except Exception as e:
            print(f"\n📊 发送状态获取: ❌ 失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 发送逻辑修复验证测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("任务队列状态", test_task_queue_status),
        ("收件人验证逻辑", test_recipient_validation_logic),
        ("并发发送逻辑", test_concurrent_sending_logic),
        ("邮件发送管理器", test_email_sending_manager)
    ]
    
    results = []
    task_status = None
    
    for test_name, test_func in tests:
        try:
            if test_name == "任务队列状态":
                result, task_status = test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    # 分析问题和建议
    if task_status:
        total_tasks = task_status['queue_stats']['total_tasks']
        if total_tasks > 0:
            print(f"\n🎉 发现 {total_tasks} 个现有任务！")
            print("💡 建议操作:")
            print("   1. 进入多浏览器发送界面")
            print("   2. 确保选择了数据源模式（不是手动输入）")
            print("   3. 点击绿色'🚀 启动'按钮")
            print("   4. 系统应该能够识别现有任务并开始发送")
        else:
            print("\n⚠️ 当前没有待发送任务")
            print("💡 建议操作:")
            print("   1. 先在数据源界面选择收件人")
            print("   2. 点击'创建发送任务'按钮")
            print("   3. 然后再尝试启动发送")
    
    if passed == total:
        print("\n🎉 所有测试通过！发送逻辑修复成功！")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
