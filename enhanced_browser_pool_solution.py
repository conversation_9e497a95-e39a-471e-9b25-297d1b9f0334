#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强版浏览器池解决方案
基于项目成功经验的全面修复版本
"""

import sys
import time
import logging
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s'
)

logger = logging.getLogger(__name__)

class EnhancedBrowserPoolSolution:
    """增强版浏览器池解决方案"""
    
    def __init__(self):
        self.cookies_dir = Path("data/cookies")
        self.browser_pool = []
        self.max_pool_size = 3
        self.current_pool_index = 0
        
        # 新浪邮箱的成功URL模式（基于项目经验）
        self.success_url_patterns = [
            'mail.sina.com.cn/classic',
            'mail.sina.com.cn/#',
            'm0.mail.sina.com.cn/classic',
            'm1.mail.sina.com.cn/classic',
            'm2.mail.sina.com.cn/classic',
            'mail.sina.com.cn/index.php'
        ]
        
        # 登录成功的多层指标（基于项目经验）
        self.login_success_indicators = {
            'url_patterns': self.success_url_patterns,
            'page_elements': ['写信', '收件箱', '发件箱', '草稿箱', '通讯录', '设置'],
            'english_elements': ['compose', 'inbox', 'sent', 'draft', 'contacts', 'settings'],
            'user_elements': ['退出', 'logout', '注销'],
            'title_keywords': ['新浪邮箱', 'sina mail', '邮箱']
        }
    
    def create_optimized_browser_pool(self):
        """创建优化的浏览器池"""
        logger.info("🏊 创建增强版浏览器池...")
        
        for i in range(self.max_pool_size):
            try:
                options = self.create_enhanced_chrome_options()
                service = Service(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=options)
                
                # 预热浏览器 - 导航到新浪邮箱首页
                driver.get("https://mail.sina.com.cn/")
                time.sleep(1)
                
                self.browser_pool.append({
                    'driver': driver,
                    'in_use': False,
                    'created_at': time.time(),
                    'last_used': 0,
                    'return_time': 0,
                    'success_count': 0,
                    'total_uses': 0
                })
                
                logger.info(f"   ✅ 浏览器实例 {i+1} 创建完成")
                
            except Exception as e:
                logger.error(f"   ❌ 浏览器实例 {i+1} 创建失败: {e}")
        
        logger.info(f"🏊 浏览器池创建完成，共 {len(self.browser_pool)} 个实例")
    
    def create_enhanced_chrome_options(self) -> ChromeOptions:
        """创建增强的Chrome选项（基于项目成功经验）"""
        options = ChromeOptions()
        
        # 基于项目成功经验的优化参数
        enhanced_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-software-rasterizer',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-hang-monitor',
            '--disable-prompt-on-repost',
            '--disable-domain-reliability',
            '--disable-component-update',
            '--disable-background-downloads',
            '--disable-client-side-phishing-detection',
            '--no-proxy-server',
            '--proxy-bypass-list=*',
            '--disable-proxy-certificate-handler',
            '--disable-background-networking',
            '--disable-default-apps',
            '--disable-sync',
            '--disable-translate',
            '--disable-plugins',
            '--disable-extensions',
            '--ignore-ssl-errors',
            '--ignore-certificate-errors',
            '--ignore-certificate-errors-spki-list',
            '--allow-running-insecure-content',
            '--disable-ssl-false-start',
            '--disable-web-security',
            '--memory-pressure-off',
            '--max_old_space_size=2048',
            '--disable-features=VizDisplayCompositor',
            '--disable-ipc-flooding-protection',
            '--disable-images',  # 禁用图片加载提升速度
            '--blink-settings=imagesEnabled=false',
            '--disable-java',
            '--disable-flash',
        ]
        
        for arg in enhanced_args:
            options.add_argument(arg)
        
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        options.add_argument('--window-size=1280,720')
        
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        return options
    
    def get_browser_from_pool(self):
        """从池中轮换获取浏览器实例"""
        if not self.browser_pool:
            logger.warning("⚠️ 浏览器池为空，创建临时实例")
            options = self.create_enhanced_chrome_options()
            service = Service(ChromeDriverManager().install())
            return webdriver.Chrome(service=service, options=options), -1
        
        # 轮换使用浏览器实例
        browser_info = self.browser_pool[self.current_pool_index]
        browser_info['in_use'] = True
        browser_info['last_used'] = time.time()
        browser_info['total_uses'] += 1
        
        current_index = self.current_pool_index
        
        # 更新索引到下一个浏览器
        self.current_pool_index = (self.current_pool_index + 1) % len(self.browser_pool)
        
        logger.info(f"🔄 使用浏览器实例 {current_index + 1}/{len(self.browser_pool)} (第{browser_info['total_uses']}次使用)")
        
        return browser_info['driver'], current_index
    
    def return_browser_to_pool(self, driver, browser_index, success=False):
        """将浏览器实例返回池中"""
        if browser_index == -1:
            # 临时创建的实例，直接关闭
            try:
                driver.quit()
            except:
                pass
            return
        
        if 0 <= browser_index < len(self.browser_pool):
            browser_info = self.browser_pool[browser_index]
            browser_info['in_use'] = False
            browser_info['return_time'] = time.time()
            
            if success:
                browser_info['success_count'] += 1
            
            # 清理浏览器状态
            try:
                driver.delete_all_cookies()
                driver.get("https://mail.sina.com.cn/")  # 重置到首页
                time.sleep(0.5)
                logger.info(f"🔄 浏览器实例 {browser_index + 1} 已返回池中 (成功率: {browser_info['success_count']}/{browser_info['total_uses']})")
            except Exception as e:
                logger.warning(f"⚠️ 清理浏览器状态失败: {e}")
        else:
            logger.warning(f"⚠️ 无效的浏览器索引: {browser_index}")
    
    def load_cookies_enhanced(self, account_email: str) -> Optional[List[Dict]]:
        """增强版Cookie加载"""
        try:
            file_name = account_email.replace("@sina.com", "_sina_com.cookies")
            cookie_file = self.cookies_dir / file_name
            
            if not cookie_file.exists() or cookie_file.stat().st_size == 0:
                return None
            
            with open(cookie_file, 'r', encoding='utf-8') as f:
                file_content = f.read().strip()
            
            if not file_content:
                return None
            
            try:
                from src.utils.encryption import get_encryption_manager
                encryption_manager = get_encryption_manager()
                decrypted_content = encryption_manager.decrypt(file_content)
                if decrypted_content:
                    cookies_data = json.loads(decrypted_content)
                else:
                    cookies_data = json.loads(file_content)
            except:
                cookies_data = json.loads(file_content)
            
            if isinstance(cookies_data, dict) and 'cookies' in cookies_data:
                cookies = cookies_data['cookies']
            elif isinstance(cookies_data, list):
                cookies = cookies_data
            else:
                return None
            
            return cookies
            
        except Exception as e:
            logger.error(f"❌ Cookie加载失败: {account_email} - {e}")
            return None
    
    def enhanced_login_verification(self, driver, account_email: str) -> Dict:
        """增强版登录状态验证（基于项目成功经验）"""
        result = {
            'success': False,
            'confidence': 0,
            'indicators_found': [],
            'verification_details': {},
            'current_url': '',
            'page_title': ''
        }
        
        try:
            # 1. 基础信息获取
            current_url = driver.current_url.lower()
            page_title = driver.title
            result['current_url'] = current_url
            result['page_title'] = page_title
            
            logger.info(f"🔍 验证登录状态 - URL: {current_url}")
            logger.info(f"🔍 页面标题: {page_title}")
            
            # 2. URL模式检查（最可靠的指标）
            url_score = 0
            for pattern in self.login_success_indicators['url_patterns']:
                if pattern.lower() in current_url:
                    url_score += 30
                    result['indicators_found'].append(f"URL匹配: {pattern}")
                    break
            
            # 3. 页面标题检查
            title_score = 0
            for keyword in self.login_success_indicators['title_keywords']:
                if keyword in page_title:
                    title_score += 20
                    result['indicators_found'].append(f"标题匹配: {keyword}")
                    break
            
            # 4. 页面内容检查
            try:
                page_source = driver.page_source
                content_score = 0
                
                # 检查中文指标
                for indicator in self.login_success_indicators['page_elements']:
                    if indicator in page_source:
                        content_score += 10
                        result['indicators_found'].append(f"页面元素: {indicator}")
                
                # 检查英文指标
                for indicator in self.login_success_indicators['english_elements']:
                    if indicator in page_source:
                        content_score += 10
                        result['indicators_found'].append(f"英文元素: {indicator}")
                
                # 检查用户相关指标
                for indicator in self.login_success_indicators['user_elements']:
                    if indicator in page_source:
                        content_score += 15
                        result['indicators_found'].append(f"用户元素: {indicator}")
                
                # 检查用户邮箱地址
                if account_email in page_source:
                    content_score += 25
                    result['indicators_found'].append(f"用户邮箱: {account_email}")
                
            except Exception as e:
                logger.warning(f"⚠️ 页面内容检查异常: {e}")
                content_score = 0
            
            # 5. 元素检查（更精确的验证）
            element_score = 0
            try:
                # 查找写信按钮
                write_elements = driver.find_elements(By.XPATH, 
                    "//a[contains(text(), '写信')] | //a[contains(text(), '写邮件')] | //a[contains(@href, 'compose')]")
                if write_elements:
                    element_score += 20
                    result['indicators_found'].append("找到写信按钮")
                
                # 查找收件箱链接
                inbox_elements = driver.find_elements(By.XPATH,
                    "//a[contains(text(), '收件箱')] | //a[contains(@href, 'inbox')]")
                if inbox_elements:
                    element_score += 15
                    result['indicators_found'].append("找到收件箱链接")
                
                # 查找用户信息
                user_elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{account_email}')]")
                if user_elements:
                    element_score += 25
                    result['indicators_found'].append("找到用户信息元素")
                
            except Exception as e:
                logger.debug(f"元素查找异常: {e}")
            
            # 6. 计算总置信度
            total_score = url_score + title_score + content_score + element_score
            result['confidence'] = min(total_score, 100)
            
            # 7. 判断登录成功
            if total_score >= 50:  # 置信度阈值
                result['success'] = True
                logger.info(f"✅ 登录验证成功 (置信度: {result['confidence']}%)")
            else:
                logger.info(f"❌ 登录验证失败 (置信度: {result['confidence']}%)")
            
            result['verification_details'] = {
                'url_score': url_score,
                'title_score': title_score,
                'content_score': content_score,
                'element_score': element_score,
                'total_score': total_score
            }
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 登录验证异常: {e}")
            result['verification_details']['error'] = str(e)
            return result
    
    def enhanced_cookie_login(self, account_email: str) -> Dict:
        """增强版Cookie登录（基于项目成功经验）"""
        logger.info(f"🚀 增强版Cookie登录: {account_email}")
        
        result = {
            'success': False,
            'message': '',
            'times': {},
            'verification_result': {},
            'browser_index': -1
        }
        
        start_total = time.time()
        
        try:
            # 1. 快速加载cookies
            start_time = time.time()
            cookies = self.load_cookies_enhanced(account_email)
            if not cookies:
                result['message'] = "无法加载cookies"
                return result
            
            result['times']['cookie_load'] = time.time() - start_time
            
            # 2. 从池中获取浏览器
            start_time = time.time()
            driver, browser_index = self.get_browser_from_pool()
            result['browser_index'] = browser_index
            result['times']['browser_get'] = time.time() - start_time
            
            # 3. 导航到新浪邮箱（确保在正确域名下）
            start_time = time.time()
            driver.set_page_load_timeout(5)
            driver.implicitly_wait(2)
            
            try:
                driver.get("https://mail.sina.com.cn/")
                time.sleep(1)  # 等待页面稳定
            except:
                pass  # 忽略超时，继续处理
            
            result['times']['navigation'] = time.time() - start_time
            
            # 4. 应用cookies
            start_time = time.time()
            driver.delete_all_cookies()
            
            applied_count = 0
            for cookie in cookies:
                try:
                    clean_cookie = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie.get('domain', '.sina.com.cn'),
                        'path': cookie.get('path', '/'),
                    }
                    driver.add_cookie(clean_cookie)
                    applied_count += 1
                except:
                    pass
            
            result['times']['cookie_apply'] = time.time() - start_time
            
            # 5. 导航到邮箱主页（关键步骤）
            start_time = time.time()
            
            try:
                # 导航到邮箱主页以激活cookies
                driver.get("https://mail.sina.com.cn/")
                time.sleep(2)  # 等待页面加载和cookies生效
                
                # 如果还在登录页面，尝试导航到经典版
                current_url = driver.current_url.lower()
                if 'login' in current_url or 'passport' in current_url:
                    logger.info("🔄 尝试导航到经典版邮箱...")
                    driver.get("https://mail.sina.com.cn/classic/")
                    time.sleep(2)
                
            except Exception as e:
                logger.warning(f"⚠️ 导航异常: {e}")
            
            result['times']['final_navigation'] = time.time() - start_time
            
            # 6. 增强版登录状态验证
            start_time = time.time()
            verification_result = self.enhanced_login_verification(driver, account_email)
            result['verification_result'] = verification_result
            result['times']['verification'] = time.time() - start_time
            
            # 7. 判断最终结果
            result['success'] = verification_result['success']
            
            if result['success']:
                result['message'] = f"登录成功 (置信度: {verification_result['confidence']}%) - 找到指示器: {verification_result['indicators_found'][:3]}"
                self.return_browser_to_pool(driver, browser_index, success=True)
            else:
                result['message'] = f"登录失败 (置信度: {verification_result['confidence']}%) - 指示器: {verification_result['indicators_found']}"
                self.return_browser_to_pool(driver, browser_index, success=False)
            
            # 8. 计算总时间
            result['times']['total'] = time.time() - start_total
            
            logger.info(f"   🎯 登录完成: {'成功' if result['success'] else '失败'} ({result['times']['total']:.3f}秒)")
            
            return result
            
        except Exception as e:
            result['times']['total'] = time.time() - start_total
            result['message'] = f"登录异常: {e}"
            logger.error(f"   ❌ 登录异常: {e}")
            
            # 确保浏览器返回池中
            if 'browser_index' in result and result['browser_index'] >= 0:
                self.return_browser_to_pool(driver, result['browser_index'], success=False)
            
            return result
