#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器简单测试脚本
测试配置管理器和浏览器管理器的基本功能
"""

import sys
import os
sys.path.insert(0, '.')

def test_config_manager():
    """测试配置管理器"""
    print("🔧 测试配置管理器...")
    
    try:
        from src.utils.config_manager import ConfigManager
        
        # 创建配置管理器
        config = ConfigManager()
        config.load_config()
        
        print("📋 配置管理器功能测试:")
        
        # 测试基本方法
        try:
            db_path = config.get_database_path()
            print(f"   ✅ get_database_path(): {db_path}")
        except Exception as e:
            print(f"   ❌ get_database_path() 失败: {e}")
            return False
        
        try:
            config_dict = config.get_all()
            print(f"   ✅ get_all(): 配置项数量 {len(config_dict)}")
        except Exception as e:
            print(f"   ❌ get_all() 失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_browser_manager():
    """测试浏览器管理器"""
    print("\n🔧 测试浏览器管理器...")
    
    try:
        from src.core.browser_manager import BrowserManager
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        # 创建依赖组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        print("📋 浏览器管理器创建测试:")
        
        # 创建浏览器管理器
        try:
            # BrowserManager只需要配置字典
            config_dict = getattr(config, 'config', {})
            browser_manager = BrowserManager(config_dict)
            print("   ✅ BrowserManager创建成功")
            
            # 检查关键方法
            has_create_driver = hasattr(browser_manager, 'create_driver')
            print(f"   📊 create_driver方法: {'存在' if has_create_driver else '不存在'}")
            
            return has_create_driver
            
        except Exception as e:
            print(f"   ❌ BrowserManager创建失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_unified_email_sender():
    """测试统一邮件发送器"""
    print("\n🔧 测试统一邮件发送器...")
    
    try:
        from src.core.unified_email_sender import UnifiedEmailSender, SendingStrategy
        
        print("📋 统一邮件发送器测试:")
        
        # 测试创建发送器（不传递driver）
        try:
            sender = UnifiedEmailSender(
                driver=None,  # 故意传递None测试
                strategy=SendingStrategy.ULTRA_FAST
            )
            print("   ✅ UnifiedEmailSender创建成功（参数正确）")
            return True
        except Exception as e:
            print(f"   ❌ UnifiedEmailSender创建失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_email_sending_manager():
    """测试邮件发送管理器"""
    print("\n🔧 测试邮件发送管理器...")
    
    try:
        from src.core.email_sending_manager import EmailSendingManager, SendingConfig
        
        print("📋 邮件发送管理器测试:")
        
        # 创建发送管理器
        config = SendingConfig()
        manager = EmailSendingManager(config)
        
        # 检查发送器工厂
        has_factory_attr = hasattr(manager, 'sender_factory')
        factory_value = getattr(manager, 'sender_factory', None)
        
        print(f"   📊 sender_factory属性: {'存在' if has_factory_attr else '不存在'}")
        print(f"   📊 sender_factory初始值: {factory_value}")
        
        # 测试设置发送器工厂
        def test_factory():
            return "test_sender"
        
        manager.set_sender_factory(test_factory)
        
        factory_after = getattr(manager, 'sender_factory', None)
        print(f"   📊 设置后sender_factory: {factory_after}")
        
        # 测试调用工厂
        if factory_after:
            try:
                result = factory_after()
                print(f"   ✅ 工厂调用成功: {result}")
                return True
            except Exception as e:
                print(f"   ❌ 工厂调用失败: {e}")
                return False
        else:
            print("   ❌ 发送器工厂设置失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_integration():
    """测试集成功能"""
    print("\n🔧 测试集成功能...")
    
    try:
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        from src.core.browser_manager import BrowserManager
        from src.core.unified_email_sender import UnifiedEmailSender, SendingStrategy
        from src.core.email_sending_manager import EmailSendingManager, SendingConfig
        
        print("📋 集成功能测试:")
        
        # 1. 创建配置和数据库管理器
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        print("   ✅ 配置和数据库管理器创建成功")
        
        # 2. 创建浏览器管理器
        config_dict = getattr(config, 'config', {})
        browser_manager = BrowserManager(config_dict)
        print("   ✅ 浏览器管理器创建成功")
        
        # 3. 创建邮件发送管理器
        sending_config = SendingConfig()
        email_manager = EmailSendingManager(sending_config)
        print("   ✅ 邮件发送管理器创建成功")
        
        # 4. 模拟发送器工厂设置
        def mock_sender_factory():
            return UnifiedEmailSender(
                driver=None,  # 模拟驱动
                strategy=SendingStrategy.ULTRA_FAST
            )
        
        email_manager.set_sender_factory(mock_sender_factory)
        print("   ✅ 发送器工厂设置成功")
        
        # 5. 测试工厂调用
        try:
            sender = email_manager.sender_factory()
            print("   ✅ 发送器工厂调用成功")
            return True
        except Exception as e:
            print(f"   ❌ 发送器工厂调用失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 配置管理器和组件集成测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("配置管理器", test_config_manager),
        ("浏览器管理器", test_browser_manager),
        ("统一邮件发送器", test_unified_email_sender),
        ("邮件发送管理器", test_email_sending_manager),
        ("集成功能", test_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有组件功能正常！")
        print("\n💡 配置管理器修复成功，现在应该能够:")
        print("   1. 正确初始化配置管理器")
        print("   2. 成功创建浏览器管理器")
        print("   3. 正常创建统一邮件发送器")
        print("   4. 正确设置发送器工厂")
        print("   5. 启动557个任务的发送")
    elif passed >= 3:
        print("🎯 大部分功能正常！")
        print("   配置管理器修复应该已经解决了主要问题")
    else:
        print("⚠️ 仍有重要问题需要解决")
    
    return passed >= 3  # 至少3个测试通过就算成功

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
