做一个windos程序
1，可以批量在浏览器自动登录新浪邮箱，导入账号+密码即可，每个账号可以单独代理ip（代理ip格式多样，可批量导入）
2，登录游览器可以设置轮换自动按设置的模版进行发送邮件
3，可以监控某个文件下面的txt文件新增的qq号码，然后转换成qq邮箱
4，实时监控新增的qq邮箱进行自动发送邮件
5，发件的登录账号邮箱按设置轮换进行发送
6，功能希望强大且智能

剔除如图我标红的“邮件发送和轻量化发送”界面模块及功能！

优化完善“多浏览器发送”模块邮件发送，开发以下：
1.开发邮件模版，制作模版的新增删除或编辑！
2.开发“主题+邮件内容”添加变量内容，提高进箱率防止落入垃圾箱！
3.开发添加邮件发送记录，添加邮件发送统计监控，可导出数据！
4.开发单个或批量发送邮件，可批量导入收件箱数据，可设置每次收件箱数量一次一个或多个！
5.开启同时发送多个邮箱时，可设置每次发送添加一个或多个固定邮箱！
6.开发用户可选择的发送模式，单个逐渐，或批量逐渐，或者其他
7.开发用户可选择的发送收件数据，导入的，还是“文件监控”的数据

导入的格式模版可以供下载，根据邮件发送，开发全面超级超级强大的“多浏览器发送系统”！

开发以下：
1.账号管理模块，若批量登录验证，需要可以选择哪些需要批量登录验证的账号！
2.单个账号登录点击“登录验证”之后不要再更多的确认按钮，确认窗口之类的，提前在用户界面可以选择好用那个！减少人工操作！
3.根据成功的经验，优化不需要人工确认验证码验证成功，可根据url识别登录成功！登录成功之后不要系统不要再弹出确认功能模块！登录验证主要功能就是登录提取cookoies！
4.根据登录验证成功的经验，需要加快速度登录！


优化速度：
1、根据成功的经验，登录验证时打开浏览器的速度太慢了，需优化反应速度！
2、输入完密码后，识别点击“登录”按钮太慢了！需优化加快速度！
3、登录成功后也反应太慢了，需加快速度！
根据登录验证成功的经验，优化完善提升速度！寻找最快最稳的速度！

为什么只有一个浏览器在工作，我需要的是一个或多个账号（多个浏览器）同时在工作发件！开发出可多个账号同时工作的！
现在的情况是没有实现，在发送配置的数量后，也没有切换cookies账号！没有监控一个账号发送的数量多少后，无感切换账号！

为什么multi_browser_sender_flow_test.py这里的五步几乎完美，但是复刻到我们的第一步策略几乎无法运行！请全面检查一下修复它！

现在情况“多浏览器发送“模块中的”多个账号（即多个浏览器），一个账号一个浏览器，并发同时发送的功能还是无法运行！还是只有一个浏览器执行工作！
是否是添加任务时未分配浏览器账号工作？
单浏览器账号执行数量后切换账号也未实现监控！
请开发强大的分配任务，以及分配发送任务，账号、浏览器，发送器强大的系统！

1.账号管理-账号发送次数列没有实现更新！开发实现！
2.账号管理添加一列状态，如频繁冷却倒计时，与发送器发现频繁后冷却衔接上时间！用户也可以编辑对应单个或批量账号的频繁冷却倒计时！也可以添加修改停用永久/正常状态，频繁冷却倒计时就是停用时间，无法进行使用、切换使用发送的账号！这样就可以使用能使用的账户进行发件！


单个或多个浏览器（多个账号）多个发送器（发送器是相同的）并发同时发件发信，还没实现！ 
添加到任务队列--判断“同时账号数”是否大于1，若大于1即需同时多账号（多浏览器）多个发送器发件！即每个浏览器相互独立线程，不能相互干扰！然后根据收件人总数÷发送模式一封还是多封÷配置账号数量=大概就等于每个账号分配收件人邮箱的数量!按此分配每个浏览器需发送的邮箱列表！按每封间隔时间，每个账号发送多少数量后（不点退出账号），原浏览器下智能切换cookie登录账号，继续点写信开始发件！ 

添加到任务队列--判断“同时账号数”是等于1，即一个浏览器一个账号一个发送器发件！
任务队列再扩展更多更多配置功能开发！还是在原来的“多浏览器发送”标签页任务队列下开发扩展！


1.浏览器窗口大小要可以设置，或最小窗口！

2.启动后发送选择的账号，每个浏览器应该是有智能选择的的，为什么呢？
除了停用的或冷却的账号，优先使用没有最长时间没有进行使用的账号发件！

3.一定要全面增强优化浏览器工厂，发送器工厂！已经启动失败好几次了！
创建浏览器一直失败，需要强大的内核处理这个项目，浏览器内强大的账号管理切换账号，强大的发送器发送邮件

全面检查整个项目发送邮件的逻辑细节，看看有哪些存在的问题需要进行修复完善优化！
