#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网络问题修复方案使用示例
展示如何在现有系统中应用网络问题修复功能
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.enhanced_browser_manager import EnhancedBrowserManager
from src.core.network_issue_resolver import NetworkIssueResolver
from src.models.account import Account

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s'
)

logger = logging.getLogger(__name__)

def example_1_basic_usage():
    """示例1: 基本使用方法"""
    print("\n" + "="*60)
    print("📋 示例1: 基本网络问题修复使用")
    print("="*60)
    
    # 1. 创建账号
    account = Account(
        email="<EMAIL>",
        password="password123"
    )
    
    # 2. 配置浏览器管理器
    config = {
        'browser_config': {
            'window_size': [1280, 720],
            'window_mode': '正常窗口',
            'headless': False,
            'disable_images': True
        }
    }
    
    # 3. 创建增强版浏览器管理器
    manager = EnhancedBrowserManager(config)
    
    # 4. 创建带网络修复的浏览器
    print("🚀 创建带网络修复的浏览器...")
    driver, result = manager.create_sina_mail_browser(account)
    
    if driver and result['success']:
        print("✅ 浏览器创建成功！")
        print(f"   网络状态: {'正常' if result['network_resolved'] else '异常'}")
        print(f"   Cookie状态: {'已应用' if result['cookie_applied'] else '未应用'}")
        print(f"   登录状态: {'已确认' if result['login_confirmed'] else '未确认'}")
        
        # 5. 使用浏览器进行操作
        print(f"   当前页面: {driver.title}")
        print(f"   当前URL: {driver.current_url}")
        
        # 6. 清理资源
        driver.quit()
        print("🧹 浏览器资源已清理")
    else:
        print("❌ 浏览器创建失败")
        print(f"   错误信息: {result.get('message', '未知错误')}")

def example_2_network_resolver_only():
    """示例2: 仅使用网络问题解决器"""
    print("\n" + "="*60)
    print("📋 示例2: 独立使用网络问题解决器")
    print("="*60)
    
    # 1. 创建网络问题解决器
    resolver = NetworkIssueResolver()
    
    # 2. 创建优化的Chrome选项
    print("🔧 创建优化的Chrome选项...")
    options = resolver.create_optimized_chrome_options()
    
    print(f"   ✅ 已配置 {len(options.arguments)} 个Chrome启动参数")
    print("   🔧 关键网络修复参数:")
    
    network_args = [arg for arg in options.arguments if any(keyword in arg for keyword in 
                   ['proxy', 'ssl', 'certificate', 'security', 'network'])]
    for arg in network_args[:5]:
        print(f"      - {arg}")
    
    # 3. 手动创建浏览器（如果需要）
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service
    from webdriver_manager.chrome import ChromeDriverManager
    
    print("🚀 使用优化选项创建浏览器...")
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        
        # 4. 测试网络连接
        print("🌐 测试网络连接...")
        success, message = resolver.test_network_connectivity(driver)
        
        if success:
            print(f"   ✅ {message}")
            print(f"   📄 页面标题: {driver.title}")
        else:
            print(f"   ❌ {message}")
        
        # 5. 清理资源
        driver.quit()
        print("🧹 浏览器资源已清理")
        
    except Exception as e:
        print(f"❌ 浏览器创建失败: {e}")

def example_3_integration_with_existing_system():
    """示例3: 与现有系统集成"""
    print("\n" + "="*60)
    print("📋 示例3: 与现有系统集成使用")
    print("="*60)
    
    # 模拟现有的浏览器管理器
    class ExistingBrowserManager:
        def __init__(self):
            self.network_resolver = NetworkIssueResolver()
        
        def create_browser_safe(self, account):
            """安全创建浏览器 - 集成网络修复"""
            try:
                # 使用网络修复的Chrome选项
                options = self.network_resolver.create_optimized_chrome_options(account)
                
                # 创建浏览器
                from selenium import webdriver
                from selenium.webdriver.chrome.service import Service
                from webdriver_manager.chrome import ChromeDriverManager
                
                service = Service(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=options)
                
                # 解决网络问题
                result = self.network_resolver.resolve_network_issues(driver, account.email)
                
                if result['success']:
                    return driver, True, "浏览器创建成功，网络问题已解决"
                else:
                    return driver, True, f"浏览器创建成功，但有网络问题: {result['message']}"
                    
            except Exception as e:
                return None, False, f"浏览器创建失败: {str(e)}"
    
    # 使用集成后的管理器
    account = Account(email="<EMAIL>", password="test123")
    manager = ExistingBrowserManager()
    
    print("🔧 使用集成的浏览器管理器...")
    driver, success, message = manager.create_browser_safe(account)
    
    if success and driver:
        print(f"✅ {message}")
        print(f"   📄 页面标题: {driver.title}")
        print(f"   🌐 当前URL: {driver.current_url}")
        
        # 清理资源
        driver.quit()
        print("🧹 浏览器资源已清理")
    else:
        print(f"❌ {message}")

def example_4_performance_monitoring():
    """示例4: 性能监控使用"""
    print("\n" + "="*60)
    print("📋 示例4: 性能监控和统计")
    print("="*60)
    
    config = {
        'browser_config': {
            'window_size': [1280, 720],
            'headless': True,  # 无头模式提升性能
            'disable_images': True
        }
    }
    
    manager = EnhancedBrowserManager(config)
    
    # 创建多个浏览器实例进行测试
    test_accounts = [
        Account(email=f"test{i}@sina.com", password="test123")
        for i in range(3)
    ]
    
    print("🧪 创建多个浏览器实例进行性能测试...")
    
    for i, account in enumerate(test_accounts, 1):
        print(f"\n   📊 测试 {i}/3: {account.email}")
        
        start_time = time.time()
        driver, result = manager.create_sina_mail_browser(account)
        elapsed = time.time() - start_time
        
        if driver and result['success']:
            print(f"      ✅ 创建成功 ({elapsed:.2f}秒)")
            driver.quit()
        else:
            print(f"      ❌ 创建失败 ({elapsed:.2f}秒)")
    
    # 获取统计信息
    stats = manager.get_success_statistics()
    print(f"\n📈 性能统计:")
    print(f"   总创建次数: {stats['total_created']}")
    print(f"   网络成功率: {stats['network_success_rate']:.1f}%")
    print(f"   Cookie成功率: {stats['cookie_success_rate']:.1f}%")
    print(f"   登录成功率: {stats['login_success_rate']:.1f}%")

def main():
    """主函数 - 运行所有示例"""
    print("🌐 网络问题修复方案使用示例")
    print("="*60)
    print("本示例展示如何在不同场景下使用网络问题修复功能")
    
    try:
        # 运行所有示例
        example_1_basic_usage()
        example_2_network_resolver_only()
        example_3_integration_with_existing_system()
        example_4_performance_monitoring()
        
        print("\n" + "="*60)
        print("🎉 所有示例运行完成！")
        print("💡 提示：")
        print("   1. 网络修复方案已验证有效")
        print("   2. 可以安全地集成到生产环境")
        print("   3. 建议根据实际需求选择合适的使用方式")
        print("   4. 定期监控性能统计以优化配置")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 示例运行异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
