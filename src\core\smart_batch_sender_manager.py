#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能批量发送管理器
解决大数据量批量发送的核心问题：
1. 按需加载，避免内存溢出
2. 智能任务分配
3. 完善的账号轮换机制
4. 真正的多浏览器并发
"""

import time
import threading
import queue
import sqlite3
import logging
from typing import Dict, List, Optional, Iterator, Tuple
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class BatchTask:
    """批量发送任务"""
    task_id: str
    to_email: str
    subject: str
    content: str
    priority: int = 0
    retry_count: int = 0
    status: str = "pending"  # pending, processing, completed, failed
    created_at: float = None
    assigned_browser: str = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()

@dataclass
class BatchSendingStats:
    """批量发送统计"""
    total_recipients: int = 0
    pending_tasks: int = 0
    processing_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    active_browsers: int = 0
    avg_send_time: float = 0.0
    estimated_completion: float = 0.0

class TaskDatabase:
    """任务数据库管理器 - 支持大数据量"""
    
    def __init__(self, db_path: str = "data/batch_tasks.db"):
        self.db_path = db_path
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS batch_tasks (
                        task_id TEXT PRIMARY KEY,
                        to_email TEXT NOT NULL,
                        subject TEXT NOT NULL,
                        content TEXT NOT NULL,
                        priority INTEGER DEFAULT 0,
                        retry_count INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'pending',
                        created_at REAL NOT NULL,
                        assigned_browser TEXT,
                        completed_at REAL,
                        error_message TEXT
                    )
                """)

                # 创建索引优化查询
                conn.execute("CREATE INDEX IF NOT EXISTS idx_status ON batch_tasks(status)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_priority ON batch_tasks(priority DESC)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_browser ON batch_tasks(assigned_browser)")

                # 提交事务
                conn.commit()
                logger.info("✅ 数据库初始化完成")
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")
            raise
    
    def add_tasks_batch(self, tasks: List[BatchTask]) -> int:
        """批量添加任务"""
        with sqlite3.connect(self.db_path) as conn:
            data = [
                (task.task_id, task.to_email, task.subject, task.content,
                 task.priority, task.retry_count, task.status, task.created_at,
                 task.assigned_browser, None, None)
                for task in tasks
            ]
            
            conn.executemany("""
                INSERT INTO batch_tasks 
                (task_id, to_email, subject, content, priority, retry_count, 
                 status, created_at, assigned_browser, completed_at, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, data)
            
            return len(data)
    
    def get_pending_tasks(self, limit: int = 100, browser_id: str = None) -> List[BatchTask]:
        """获取待处理任务"""
        with sqlite3.connect(self.db_path) as conn:
            if browser_id:
                cursor = conn.execute("""
                    SELECT task_id, to_email, subject, content, priority, retry_count, 
                           status, created_at, assigned_browser
                    FROM batch_tasks 
                    WHERE status = 'pending' AND (assigned_browser IS NULL OR assigned_browser = ?)
                    ORDER BY priority DESC, created_at ASC
                    LIMIT ?
                """, (browser_id, limit))
            else:
                cursor = conn.execute("""
                    SELECT task_id, to_email, subject, content, priority, retry_count, 
                           status, created_at, assigned_browser
                    FROM batch_tasks 
                    WHERE status = 'pending'
                    ORDER BY priority DESC, created_at ASC
                    LIMIT ?
                """, (limit,))
            
            tasks = []
            for row in cursor.fetchall():
                task = BatchTask(
                    task_id=row[0], to_email=row[1], subject=row[2], content=row[3],
                    priority=row[4], retry_count=row[5], status=row[6],
                    created_at=row[7], assigned_browser=row[8]
                )
                tasks.append(task)
            
            return tasks
    
    def update_task_status(self, task_id: str, status: str, browser_id: str = None, error_message: str = None):
        """更新任务状态"""
        with sqlite3.connect(self.db_path) as conn:
            if status == 'completed':
                conn.execute("""
                    UPDATE batch_tasks 
                    SET status = ?, completed_at = ?, assigned_browser = ?
                    WHERE task_id = ?
                """, (status, time.time(), browser_id, task_id))
            elif status == 'failed':
                conn.execute("""
                    UPDATE batch_tasks 
                    SET status = ?, retry_count = retry_count + 1, error_message = ?, assigned_browser = ?
                    WHERE task_id = ?
                """, (status, error_message, browser_id, task_id))
            else:
                conn.execute("""
                    UPDATE batch_tasks 
                    SET status = ?, assigned_browser = ?
                    WHERE task_id = ?
                """, (status, browser_id, task_id))
    
    def get_stats(self) -> BatchSendingStats:
        """获取发送统计"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT status, COUNT(*) 
                FROM batch_tasks 
                GROUP BY status
            """)
            
            stats = BatchSendingStats()
            for status, count in cursor.fetchall():
                if status == 'pending':
                    stats.pending_tasks = count
                elif status == 'processing':
                    stats.processing_tasks = count
                elif status == 'completed':
                    stats.completed_tasks = count
                elif status == 'failed':
                    stats.failed_tasks = count
            
            stats.total_recipients = (stats.pending_tasks + stats.processing_tasks + 
                                    stats.completed_tasks + stats.failed_tasks)
            
            return stats
    
    def clear_all_tasks(self):
        """清空所有任务"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("DELETE FROM batch_tasks")

class SmartTaskDispatcher:
    """智能任务分配器"""
    
    def __init__(self, task_db: TaskDatabase):
        self.task_db = task_db
        self.browser_queues: Dict[str, queue.Queue] = {}
        self.browser_stats: Dict[str, dict] = {}
        self.dispatch_lock = threading.Lock()
        self.is_running = False
        self.dispatch_thread = None
    
    def register_browser(self, browser_id: str):
        """注册浏览器"""
        with self.dispatch_lock:
            self.browser_queues[browser_id] = queue.Queue(maxsize=10)  # 每个浏览器最多10个任务
            self.browser_stats[browser_id] = {
                'assigned_tasks': 0,
                'completed_tasks': 0,
                'failed_tasks': 0,
                'avg_time': 0.0,
                'last_activity': time.time()
            }
            logger.info(f"📝 注册浏览器: {browser_id}")
    
    def unregister_browser(self, browser_id: str):
        """注销浏览器"""
        with self.dispatch_lock:
            if browser_id in self.browser_queues:
                del self.browser_queues[browser_id]
            if browser_id in self.browser_stats:
                del self.browser_stats[browser_id]
            logger.info(f"📝 注销浏览器: {browser_id}")
    
    def start_dispatching(self):
        """开始任务分配"""
        if not self.is_running:
            self.is_running = True
            self.dispatch_thread = threading.Thread(target=self._dispatch_loop, daemon=True)
            self.dispatch_thread.start()
            logger.info("🚀 智能任务分配器已启动")
    
    def stop_dispatching(self):
        """停止任务分配"""
        self.is_running = False
        if self.dispatch_thread:
            self.dispatch_thread.join(timeout=5)
        logger.info("🛑 智能任务分配器已停止")
    
    def _dispatch_loop(self):
        """任务分配循环"""
        while self.is_running:
            try:
                self._dispatch_tasks()
                time.sleep(1)  # 每秒检查一次
            except Exception as e:
                logger.error(f"❌ 任务分配异常: {e}")
                time.sleep(5)
    
    def _dispatch_tasks(self):
        """分配任务到浏览器"""
        with self.dispatch_lock:
            # 获取可用的浏览器（队列未满）
            available_browsers = [
                browser_id for browser_id, q in self.browser_queues.items()
                if q.qsize() < q.maxsize
            ]
            
            if not available_browsers:
                return
            
            # 按性能排序浏览器（优先分配给性能好的）
            available_browsers.sort(key=lambda bid: self.browser_stats[bid]['avg_time'])
            
            # 为每个可用浏览器分配任务
            for browser_id in available_browsers:
                queue_obj = self.browser_queues[browser_id]
                available_slots = queue_obj.maxsize - queue_obj.qsize()
                
                if available_slots > 0:
                    # 获取待处理任务
                    tasks = self.task_db.get_pending_tasks(limit=available_slots, browser_id=browser_id)
                    
                    for task in tasks:
                        # 分配任务给浏览器
                        task.assigned_browser = browser_id
                        task.status = 'processing'
                        
                        # 更新数据库
                        self.task_db.update_task_status(task.task_id, 'processing', browser_id)
                        
                        # 添加到浏览器队列
                        queue_obj.put(task)
                        
                        # 更新统计
                        self.browser_stats[browser_id]['assigned_tasks'] += 1
                        
                        logger.debug(f"📤 分配任务 {task.task_id} 给浏览器 {browser_id}")
    
    def get_task_for_browser(self, browser_id: str, timeout: float = 1.0) -> Optional[BatchTask]:
        """浏览器获取任务"""
        if browser_id not in self.browser_queues:
            return None
        
        try:
            task = self.browser_queues[browser_id].get(timeout=timeout)
            self.browser_stats[browser_id]['last_activity'] = time.time()
            return task
        except queue.Empty:
            return None
    
    def report_task_completion(self, browser_id: str, task_id: str, success: bool, 
                             execution_time: float, error_message: str = None):
        """报告任务完成情况"""
        with self.dispatch_lock:
            if browser_id in self.browser_stats:
                stats = self.browser_stats[browser_id]
                
                if success:
                    stats['completed_tasks'] += 1
                    self.task_db.update_task_status(task_id, 'completed', browser_id)
                else:
                    stats['failed_tasks'] += 1
                    self.task_db.update_task_status(task_id, 'failed', browser_id, error_message)
                
                # 更新平均执行时间
                total_tasks = stats['completed_tasks'] + stats['failed_tasks']
                if total_tasks > 0:
                    stats['avg_time'] = ((stats['avg_time'] * (total_tasks - 1)) + execution_time) / total_tasks
                
                logger.debug(f"📊 浏览器 {browser_id} 任务完成: {'成功' if success else '失败'} ({execution_time:.2f}s)")

class SmartAccountManager:
    """智能账号管理器 - 完善的账号轮换和Cookie管理"""

    def __init__(self, accounts: List, emails_per_account: int = 10):
        self.accounts = accounts
        self.emails_per_account = emails_per_account
        self.browser_accounts: Dict[str, dict] = {}  # browser_id -> account_info
        self.account_usage: Dict[str, int] = {}  # account_email -> usage_count
        self.account_lock = threading.Lock()

        # 初始化账号使用统计
        for account in accounts:
            self.account_usage[account.email] = 0

    def assign_account_to_browser(self, browser_id: str) -> Optional[dict]:
        """为浏览器分配账号"""
        with self.account_lock:
            # 查找使用次数最少且可用的账号（过滤冷却中的账号）
            available_accounts = [
                account for account in self.accounts
                if (self.account_usage[account.email] < self.emails_per_account and
                    account.is_available_for_sending())
            ]

            if not available_accounts:
                logger.warning("⚠️ 所有账号都已达到发送限制或正在冷却中")
                return None

            # 选择使用次数最少的账号
            selected_account = min(available_accounts,
                                 key=lambda acc: self.account_usage[acc.email])

            # 记录分配
            self.browser_accounts[browser_id] = {
                'account': selected_account,
                'sent_count': 0,
                'assigned_at': time.time()
            }

            logger.info(f"📧 为浏览器 {browser_id} 分配账号: {selected_account.email}")
            return self.browser_accounts[browser_id]

    def record_email_sent(self, browser_id: str) -> bool:
        """记录邮件发送，检查是否需要切换账号"""
        with self.account_lock:
            if browser_id not in self.browser_accounts:
                return False

            browser_info = self.browser_accounts[browser_id]
            browser_info['sent_count'] += 1

            account_email = browser_info['account'].email
            self.account_usage[account_email] += 1

            logger.debug(f"📊 浏览器 {browser_id} 已发送: {browser_info['sent_count']}/{self.emails_per_account}")

            # 检查是否需要切换账号
            if browser_info['sent_count'] >= self.emails_per_account:
                logger.info(f"🔄 浏览器 {browser_id} 需要切换账号")
                return self._switch_account_for_browser(browser_id)

            return True

    def _switch_account_for_browser(self, browser_id: str) -> bool:
        """为浏览器切换账号"""
        # 释放当前账号
        if browser_id in self.browser_accounts:
            del self.browser_accounts[browser_id]

        # 分配新账号
        new_account_info = self.assign_account_to_browser(browser_id)
        return new_account_info is not None

    def get_browser_account(self, browser_id: str) -> Optional[dict]:
        """获取浏览器当前账号信息"""
        return self.browser_accounts.get(browser_id)

    def reset_all_accounts(self):
        """重置所有账号使用统计"""
        with self.account_lock:
            for email in self.account_usage:
                self.account_usage[email] = 0
            self.browser_accounts.clear()
            logger.info("🔄 所有账号使用统计已重置")

class SmartBatchSenderManager:
    """智能批量发送管理器 - 主控制器"""

    def __init__(self, config):
        self.config = config
        self.task_db = TaskDatabase()
        self.dispatcher = SmartTaskDispatcher(self.task_db)
        self.account_manager = SmartAccountManager(
            config.accounts,
            config.emails_per_account
        )

        self.browser_workers: Dict[str, threading.Thread] = {}
        self.is_running = False
        self.stats_lock = threading.Lock()

        logger.info("🚀 智能批量发送管理器初始化完成")

    def import_recipients_from_file(self, file_path: str, subject: str, content: str,
                                  chunk_size: int = 1000) -> int:
        """从文件导入收件人 - 分块处理避免内存溢出"""
        logger.info(f"📥 开始导入收件人文件: {file_path}")

        total_imported = 0
        chunk_count = 0

        try:
            # 使用生成器逐块读取文件
            for email_chunk in self._read_emails_in_chunks(file_path, chunk_size):
                chunk_count += 1
                tasks = []

                for email_data in email_chunk:
                    task_id = f"batch_{int(time.time() * 1000)}_{total_imported}"

                    # 应用变量替换
                    final_subject = self._apply_variables(subject, email_data)
                    final_content = self._apply_variables(content, email_data)

                    task = BatchTask(
                        task_id=task_id,
                        to_email=email_data['email'],
                        subject=final_subject,
                        content=final_content,
                        priority=0
                    )
                    tasks.append(task)

                # 批量添加到数据库
                added_count = self.task_db.add_tasks_batch(tasks)
                total_imported += added_count

                logger.info(f"📦 导入第 {chunk_count} 批: {added_count} 个任务 (总计: {total_imported})")

                # 避免内存占用过多，及时清理
                del tasks
                del email_chunk

            logger.info(f"✅ 导入完成，总计: {total_imported} 个任务")
            return total_imported

        except Exception as e:
            logger.error(f"❌ 导入失败: {e}")
            return total_imported

    def _read_emails_in_chunks(self, file_path: str, chunk_size: int) -> Iterator[List[dict]]:
        """分块读取邮件文件"""
        import pandas as pd

        # 根据文件类型选择读取方法
        if file_path.endswith('.csv'):
            # 使用pandas的chunksize参数分块读取
            for chunk_df in pd.read_csv(file_path, chunksize=chunk_size):
                yield chunk_df.to_dict('records')

        elif file_path.endswith('.xlsx'):
            # Excel文件一次性读取，然后分块
            df = pd.read_excel(file_path)
            for i in range(0, len(df), chunk_size):
                chunk_df = df.iloc[i:i + chunk_size]
                yield chunk_df.to_dict('records')

        elif file_path.endswith('.txt'):
            # 文本文件逐行读取
            chunk = []
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    email = line.strip()
                    if email and '@' in email:
                        chunk.append({'email': email, 'name': email.split('@')[0]})

                        if len(chunk) >= chunk_size:
                            yield chunk
                            chunk = []

                # 处理最后一批
                if chunk:
                    yield chunk

    def _apply_variables(self, text: str, email_data: dict) -> str:
        """应用变量替换"""
        for key, value in email_data.items():
            if value:
                placeholder = f"{{{key}}}"
                text = text.replace(placeholder, str(value))
        return text

    def start_batch_sending(self, max_browsers: int = 3):
        """开始批量发送"""
        if self.is_running:
            logger.warning("⚠️ 批量发送已在运行中")
            return

        self.is_running = True

        # 启动任务分配器
        self.dispatcher.start_dispatching()

        # 启动浏览器工作线程
        for i in range(max_browsers):
            browser_id = f"browser_{i + 1}"
            self.dispatcher.register_browser(browser_id)

            worker_thread = threading.Thread(
                target=self._browser_worker,
                args=(browser_id,),
                daemon=True,
                name=f"BatchWorker-{browser_id}"
            )

            self.browser_workers[browser_id] = worker_thread
            worker_thread.start()

            logger.info(f"🚀 启动浏览器工作线程: {browser_id}")

        logger.info(f"✅ 批量发送已启动，使用 {max_browsers} 个浏览器")

    def stop_batch_sending(self):
        """停止批量发送"""
        if not self.is_running:
            return

        logger.info("🛑 正在停止批量发送...")

        self.is_running = False

        # 停止任务分配器
        self.dispatcher.stop_dispatching()

        # 等待所有工作线程完成
        for browser_id, worker_thread in self.browser_workers.items():
            self.dispatcher.unregister_browser(browser_id)
            worker_thread.join(timeout=10)
            logger.info(f"🛑 浏览器工作线程已停止: {browser_id}")

        self.browser_workers.clear()
        logger.info("✅ 批量发送已完全停止")

    def _browser_worker(self, browser_id: str):
        """浏览器工作线程"""
        logger.info(f"🔧 浏览器工作线程启动: {browser_id}")

        # 为浏览器分配账号
        account_info = self.account_manager.assign_account_to_browser(browser_id)
        if not account_info:
            logger.error(f"❌ 无法为浏览器 {browser_id} 分配账号")
            return

        # 这里应该初始化实际的浏览器实例和发送器
        # browser_instance = self._create_browser_instance(browser_id, account_info)
        # sender = self._create_sender(browser_instance)

        while self.is_running:
            try:
                # 获取任务
                task = self.dispatcher.get_task_for_browser(browser_id, timeout=5.0)
                if not task:
                    continue

                # 检查账号状态
                if not self.account_manager.record_email_sent(browser_id):
                    logger.warning(f"⚠️ 浏览器 {browser_id} 账号切换失败")
                    self.dispatcher.report_task_completion(
                        browser_id, task.task_id, False, 0, "账号切换失败"
                    )
                    continue

                # 执行发送任务
                start_time = time.time()
                success, error_message = self._send_email_task(browser_id, task)
                execution_time = time.time() - start_time

                # 报告任务完成情况
                self.dispatcher.report_task_completion(
                    browser_id, task.task_id, success, execution_time, error_message
                )

                if success:
                    logger.info(f"✅ 任务完成: {task.task_id} -> {task.to_email} ({execution_time:.2f}s)")
                else:
                    logger.error(f"❌ 任务失败: {task.task_id} -> {error_message}")

            except Exception as e:
                logger.error(f"❌ 浏览器工作线程异常 {browser_id}: {e}")
                time.sleep(5)

        logger.info(f"🛑 浏览器工作线程结束: {browser_id}")

    def _send_email_task(self, browser_id: str, task: BatchTask) -> Tuple[bool, str]:
        """执行邮件发送任务 - 这里需要集成实际的发送逻辑"""
        try:
            # 这里应该调用实际的发送器
            # success = sender.send_email_ultra_fast(task.to_email, task.subject, task.content)

            # 模拟发送过程
            time.sleep(1.5)  # 模拟发送时间

            # 模拟90%成功率
            import random
            success = random.random() > 0.1

            if success:
                return True, None
            else:
                return False, "模拟发送失败"

        except Exception as e:
            return False, str(e)

    def get_sending_stats(self) -> BatchSendingStats:
        """获取发送统计"""
        stats = self.task_db.get_stats()
        stats.active_browsers = len([w for w in self.browser_workers.values() if w.is_alive()])
        return stats

    def clear_all_tasks(self):
        """清空所有任务"""
        self.task_db.clear_all_tasks()
        logger.info("🗑️ 所有任务已清空")
