#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能任务分配器
根据账号策略智能分配邮件发送任务到不同的浏览器和账号
"""

import time
import uuid
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass, field
from collections import defaultdict
from loguru import logger

from src.models.account import Account
from .smart_account_strategy import AccountStrategy, BrowserMode, SendingMode


@dataclass
class EmailTask:
    """邮件任务"""
    task_id: str
    browser_id: int
    account_email: str
    to_emails: List[str]  # 支持批量发送
    cc_email: Optional[str]
    subject: str
    content: str
    content_type: str = "text/plain"
    priority: int = 1
    created_time: float = field(default_factory=time.time)
    status: str = "pending"
    retry_count: int = 0
    max_retries: int = 3


@dataclass
class BrowserTaskQueue:
    """浏览器任务队列"""
    browser_id: int
    assigned_accounts: List[Account]
    task_queue: List[EmailTask] = field(default_factory=list)
    current_account_index: int = 0
    account_send_counts: Dict[str, int] = field(default_factory=dict)  # 每个账号的发送计数
    
    def get_current_account(self) -> Optional[Account]:
        """获取当前账号"""
        if self.assigned_accounts and 0 <= self.current_account_index < len(self.assigned_accounts):
            return self.assigned_accounts[self.current_account_index]
        return None
    
    def should_switch_account(self, switch_threshold: int) -> bool:
        """判断是否需要切换账号"""
        current_account = self.get_current_account()
        if not current_account:
            return True
        
        current_count = self.account_send_counts.get(current_account.email, 0)
        return current_count >= switch_threshold
    
    def switch_to_next_account(self):
        """切换到下一个账号"""
        if len(self.assigned_accounts) > 1:
            self.current_account_index = (self.current_account_index + 1) % len(self.assigned_accounts)
            logger.info(f"浏览器{self.browser_id}切换到账号: {self.get_current_account().email}")
    
    def increment_send_count(self, account_email: str):
        """增加账号发送计数"""
        self.account_send_counts[account_email] = self.account_send_counts.get(account_email, 0) + 1


class IntelligentTaskDistributor:
    """智能任务分配器"""
    
    def __init__(self):
        self.logger = logger
        self.browser_queues: Dict[int, BrowserTaskQueue] = {}
        self.global_task_counter = 0
    
    def distribute_tasks(
        self,
        strategy: AccountStrategy,
        recipient_emails: List[str],
        subject: str,
        content: str,
        content_type: str = "text/plain",
        cc_email: Optional[str] = None,
        sending_mode: SendingMode = SendingMode.INDIVIDUAL,
        batch_size: int = 1
    ) -> Dict[int, BrowserTaskQueue]:
        """
        根据策略分配任务
        
        Args:
            strategy: 账号策略
            recipient_emails: 收件人邮箱列表
            subject: 邮件主题
            content: 邮件内容
            content_type: 内容类型
            cc_email: 抄送邮箱
            sending_mode: 发送模式
            batch_size: 批量大小（批量发送模式下使用）
            
        Returns:
            Dict[int, BrowserTaskQueue]: 浏览器任务队列字典
        """
        try:
            self.logger.info(f"🎯 开始智能任务分配...")
            self.logger.info(f"   - 收件人数量：{len(recipient_emails)}")
            self.logger.info(f"   - 浏览器数量：{strategy.browser_count}")
            self.logger.info(f"   - 发送模式：{sending_mode.value}")
            
            # 初始化浏览器队列
            self._initialize_browser_queues(strategy)
            
            # 根据发送模式分配任务
            if sending_mode == SendingMode.INDIVIDUAL:
                self._distribute_individual_tasks(
                    strategy, recipient_emails, subject, content, content_type, cc_email
                )
            else:
                self._distribute_batch_tasks(
                    strategy, recipient_emails, subject, content, content_type, cc_email, batch_size
                )
            
            # 记录分配结果
            self._log_distribution_summary()
            
            return self.browser_queues
            
        except Exception as e:
            self.logger.error(f"任务分配失败: {e}")
            raise
    
    def _initialize_browser_queues(self, strategy: AccountStrategy):
        """初始化浏览器队列"""
        self.browser_queues.clear()
        
        for i, accounts in enumerate(strategy.accounts_per_browser):
            self.browser_queues[i] = BrowserTaskQueue(
                browser_id=i,
                assigned_accounts=accounts
            )
            
            self.logger.info(f"初始化浏览器{i}队列，分配账号：{[acc.email for acc in accounts]}")
    
    def _distribute_individual_tasks(
        self,
        strategy: AccountStrategy,
        recipient_emails: List[str],
        subject: str,
        content: str,
        content_type: str,
        cc_email: Optional[str]
    ):
        """分配单独发送任务"""
        
        # 计算每个浏览器应该处理的邮件数量
        emails_per_browser = self._calculate_emails_per_browser(
            len(recipient_emails), strategy.browser_count
        )
        
        email_index = 0
        for browser_id, email_count in enumerate(emails_per_browser):
            if browser_id not in self.browser_queues:
                continue
            
            browser_queue = self.browser_queues[browser_id]
            
            # 为这个浏览器分配邮件
            for i in range(email_count):
                if email_index >= len(recipient_emails):
                    break
                
                # 获取当前账号
                current_account = browser_queue.get_current_account()
                if not current_account:
                    self.logger.warning(f"浏览器{browser_id}没有可用账号")
                    break
                
                # 创建任务
                task = EmailTask(
                    task_id=f"task_{self.global_task_counter}_{int(time.time())}",
                    browser_id=browser_id,
                    account_email=current_account.email,
                    to_emails=[recipient_emails[email_index]],
                    cc_email=cc_email,
                    subject=subject,
                    content=content,
                    content_type=content_type
                )
                
                browser_queue.task_queue.append(task)
                self.global_task_counter += 1
                email_index += 1
                
                # 检查是否需要切换账号
                browser_queue.increment_send_count(current_account.email)
                if browser_queue.should_switch_account(strategy.switch_threshold):
                    browser_queue.switch_to_next_account()
    
    def _distribute_batch_tasks(
        self,
        strategy: AccountStrategy,
        recipient_emails: List[str],
        subject: str,
        content: str,
        content_type: str,
        cc_email: Optional[str],
        batch_size: int
    ):
        """分配批量发送任务"""
        
        # 将收件人分批
        email_batches = []
        for i in range(0, len(recipient_emails), batch_size):
            batch = recipient_emails[i:i + batch_size]
            email_batches.append(batch)
        
        # 计算每个浏览器应该处理的批次数量
        batches_per_browser = self._calculate_emails_per_browser(
            len(email_batches), strategy.browser_count
        )
        
        batch_index = 0
        for browser_id, batch_count in enumerate(batches_per_browser):
            if browser_id not in self.browser_queues:
                continue
            
            browser_queue = self.browser_queues[browser_id]
            
            # 为这个浏览器分配批次
            for i in range(batch_count):
                if batch_index >= len(email_batches):
                    break
                
                # 获取当前账号
                current_account = browser_queue.get_current_account()
                if not current_account:
                    self.logger.warning(f"浏览器{browser_id}没有可用账号")
                    break
                
                # 创建批量任务
                task = EmailTask(
                    task_id=f"batch_task_{self.global_task_counter}_{int(time.time())}",
                    browser_id=browser_id,
                    account_email=current_account.email,
                    to_emails=email_batches[batch_index],
                    cc_email=cc_email,
                    subject=subject,
                    content=content,
                    content_type=content_type
                )
                
                browser_queue.task_queue.append(task)
                self.global_task_counter += 1
                batch_index += 1
                
                # 检查是否需要切换账号
                browser_queue.increment_send_count(current_account.email)
                if browser_queue.should_switch_account(strategy.switch_threshold):
                    browser_queue.switch_to_next_account()
    
    def _calculate_emails_per_browser(self, total_items: int, browser_count: int) -> List[int]:
        """计算每个浏览器应该处理的项目数量"""
        if browser_count == 0:
            return []
        
        base_count = total_items // browser_count
        remainder = total_items % browser_count
        
        result = []
        for i in range(browser_count):
            count = base_count + (1 if i < remainder else 0)
            result.append(count)
        
        return result
    
    def _log_distribution_summary(self):
        """记录分配摘要"""
        self.logger.info(f"📊 任务分配完成摘要：")
        
        total_tasks = 0
        for browser_id, queue in self.browser_queues.items():
            task_count = len(queue.task_queue)
            total_tasks += task_count
            
            account_emails = [acc.email for acc in queue.assigned_accounts]
            self.logger.info(f"   - 浏览器{browser_id}：{task_count}个任务，账号：{account_emails}")
        
        self.logger.info(f"   - 总任务数：{total_tasks}")
    
    def get_browser_queue(self, browser_id: int) -> Optional[BrowserTaskQueue]:
        """获取指定浏览器的任务队列"""
        return self.browser_queues.get(browser_id)
    
    def get_all_queues(self) -> Dict[int, BrowserTaskQueue]:
        """获取所有浏览器队列"""
        return self.browser_queues.copy()
    
    def get_total_task_count(self) -> int:
        """获取总任务数量"""
        return sum(len(queue.task_queue) for queue in self.browser_queues.values())
    
    def get_pending_task_count(self) -> int:
        """获取待处理任务数量"""
        return sum(
            len([task for task in queue.task_queue if task.status == "pending"])
            for queue in self.browser_queues.values()
        )
