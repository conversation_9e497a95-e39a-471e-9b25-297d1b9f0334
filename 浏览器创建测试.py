#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器创建测试脚本
测试修复后的浏览器创建流程
"""

import sys
import os
sys.path.insert(0, '.')

def test_browser_creation():
    """测试浏览器创建流程"""
    print("🔧 测试浏览器创建流程...")
    
    try:
        from src.core.browser_manager import BrowserManager
        from src.models.account import AccountManager, Account
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        print("📋 浏览器创建测试:")
        
        # 1. 创建浏览器管理器
        config_dict = getattr(config, 'config', {})
        browser_config = config_dict.get('browser', {})
        browser_config['window_size'] = [800, 600]
        browser_config['window_mode'] = '最小化窗口'
        config_dict['browser'] = browser_config
        
        browser_manager = BrowserManager(config_dict)
        print("   ✅ 浏览器管理器创建成功")
        
        # 2. 获取可用账号
        account_manager = AccountManager(db_manager)
        available_accounts = account_manager.get_available_accounts()
        
        if not available_accounts:
            print("   ❌ 没有可用账号")
            return False
        
        print(f"   📊 找到 {len(available_accounts)} 个可用账号")
        
        # 3. 测试创建浏览器驱动
        test_account = available_accounts[0]
        print(f"   🧪 测试账号: {test_account.email}")
        
        try:
            # 测试create_driver方法
            driver_id = browser_manager.create_driver(test_account)
            if driver_id:
                print(f"   ✅ 浏览器驱动创建成功: {driver_id}")
                
                # 测试get_driver方法
                driver = browser_manager.get_driver(driver_id)
                if driver:
                    print("   ✅ 浏览器驱动获取成功")
                    
                    # 测试驱动功能
                    try:
                        driver.get("about:blank")
                        print("   ✅ 浏览器功能测试成功")
                        
                        # 清理
                        driver.quit()
                        print("   ✅ 浏览器清理成功")
                        return True
                    except Exception as e:
                        print(f"   ❌ 浏览器功能测试失败: {e}")
                        try:
                            driver.quit()
                        except:
                            pass
                        return False
                else:
                    print("   ❌ 浏览器驱动获取失败")
                    return False
            else:
                print("   ❌ 浏览器驱动创建失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 浏览器创建测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_smart_account_selection():
    """测试智能账号选择"""
    print("\n🔧 测试智能账号选择...")
    
    try:
        from src.models.account import AccountManager
        from src.utils.config_manager import ConfigManager
        from src.models.database import DatabaseManager
        from datetime import datetime
        
        # 初始化组件
        config = ConfigManager()
        config.load_config()
        db_path = config.get_database_path()
        db_manager = DatabaseManager(str(db_path))
        
        print("📋 智能账号选择测试:")
        
        # 获取账号管理器
        account_manager = AccountManager(db_manager)
        available_accounts = account_manager.get_available_accounts()
        
        if not available_accounts:
            print("   ❌ 没有可用账号")
            return False
        
        print(f"   📊 找到 {len(available_accounts)} 个可用账号")
        
        # 按最后使用时间排序测试
        def get_last_used_time(account):
            if account.last_used is None:
                return datetime.min
            return account.last_used
        
        sorted_accounts = sorted(available_accounts, key=get_last_used_time)
        
        print("   🎯 账号排序结果:")
        for i, account in enumerate(sorted_accounts[:5]):  # 只显示前5个
            last_used_str = "从未使用" if account.last_used is None else account.last_used.strftime("%Y-%m-%d %H:%M:%S")
            print(f"      {i+1}. {account.email} (最后使用: {last_used_str})")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能账号选择测试失败: {e}")
        return False

def test_window_configuration():
    """测试窗口配置"""
    print("\n🔧 测试窗口配置...")
    
    try:
        from src.core.browser_manager import BrowserManager
        
        print("📋 窗口配置测试:")
        
        # 测试不同窗口模式
        window_modes = [
            ("正常窗口", [1280, 720]),
            ("最小化窗口", [800, 600]),
            ("无头模式", [1024, 768])
        ]
        
        for mode, size in window_modes:
            try:
                config_dict = {
                    'browser': {
                        'window_mode': mode,
                        'window_size': size
                    }
                }
                
                browser_manager = BrowserManager(config_dict)
                print(f"   ✅ {mode} 配置创建成功 ({size[0]}x{size[1]})")
                
            except Exception as e:
                print(f"   ❌ {mode} 配置创建失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 窗口配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 浏览器创建和配置测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("智能账号选择", test_smart_account_selection),
        ("窗口配置", test_window_configuration),
        ("浏览器创建", test_browser_creation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！浏览器创建功能正常")
        print("\n💡 现在应该能够:")
        print("   1. 正确创建浏览器驱动")
        print("   2. 智能选择账号")
        print("   3. 配置窗口模式")
        print("   4. 启动557个任务的发送")
    elif passed >= 2:
        print("🎯 大部分功能正常！")
        print("   浏览器创建应该已经可以工作")
    else:
        print("⚠️ 仍有重要问题需要解决")
    
    return passed >= 2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
