#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化浏览器测试脚本
使用最保守的配置测试浏览器启动
"""

import sys
import os
import time
import tempfile
import subprocess
sys.path.insert(0, '.')

def cleanup_chrome():
    """清理Chrome进程"""
    try:
        subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe'], 
                      capture_output=True, timeout=5)
        subprocess.run(['taskkill', '/F', '/IM', 'chromedriver.exe'], 
                      capture_output=True, timeout=5)
        print("   🧹 已清理Chrome进程")
    except:
        pass

def test_basic_browser():
    """测试基础浏览器启动"""
    print("🔧 测试基础浏览器启动...")
    
    cleanup_chrome()
    time.sleep(2)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        chrome_options = Options()
        
        # 最基础的配置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--headless=new')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--no-first-run')
        chrome_options.add_argument('--disable-default-apps')
        chrome_options.add_argument('--disable-popup-blocking')
        chrome_options.add_argument('--disable-translate')
        chrome_options.add_argument('--disable-background-networking')
        chrome_options.add_argument('--disable-sync')
        chrome_options.add_argument('--log-level=3')
        
        # 使用临时目录
        user_data_dir = os.path.join(tempfile.gettempdir(), f"chrome_test_{int(time.time())}")
        os.makedirs(user_data_dir, exist_ok=True)
        chrome_options.add_argument(f'--user-data-dir={user_data_dir}')
        
        # 创建服务
        service = Service()
        
        print("   🚀 正在启动Chrome浏览器...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        
        print("   ✅ 浏览器启动成功")
        
        # 简单测试
        driver.get("data:text/html,<html><body><h1>Test Page</h1></body></html>")
        print(f"   📄 页面标题: {driver.title}")
        
        # 关闭浏览器
        driver.quit()
        print("   🔒 浏览器已关闭")
        
        # 清理临时目录
        import shutil
        try:
            shutil.rmtree(user_data_dir)
            print("   🧹 临时目录已清理")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"   ❌ 浏览器启动失败: {e}")
        return False

def test_fallback_browser():
    """测试回退浏览器配置"""
    print("\n🔧 测试回退浏览器配置...")
    
    cleanup_chrome()
    time.sleep(2)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        chrome_options = Options()
        
        # 极简配置
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--remote-debugging-port=9223')
        
        print("   🚀 正在启动回退配置浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        
        print("   ✅ 回退配置浏览器启动成功")
        
        # 关闭浏览器
        driver.quit()
        print("   🔒 浏览器已关闭")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 回退配置浏览器启动失败: {e}")
        return False

def test_system_info():
    """测试系统信息"""
    print("\n🔧 检查系统信息...")
    
    try:
        # 检查Chrome版本
        try:
            result = subprocess.run(['chrome', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"   📱 Chrome版本: {result.stdout.strip()}")
            else:
                print("   ⚠️ 无法获取Chrome版本")
        except:
            print("   ⚠️ Chrome可能未正确安装")
        
        # 检查ChromeDriver
        try:
            result = subprocess.run(['chromedriver', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"   🔧 ChromeDriver版本: {result.stdout.strip()}")
            else:
                print("   ⚠️ 无法获取ChromeDriver版本")
        except:
            print("   ⚠️ ChromeDriver可能未正确安装")
        
        # 检查内存
        try:
            import psutil
            memory = psutil.virtual_memory()
            print(f"   💾 系统内存: {memory.total // (1024**3)}GB (可用: {memory.available // (1024**3)}GB)")
        except:
            print("   ⚠️ 无法获取内存信息")
        
        # 检查临时目录权限
        try:
            temp_dir = tempfile.gettempdir()
            test_file = os.path.join(temp_dir, "test_write.txt")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            print(f"   📁 临时目录可写: {temp_dir}")
        except:
            print("   ❌ 临时目录写入权限有问题")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 系统信息检查失败: {e}")
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 浏览器启动问题解决建议:")
    print("=" * 50)
    
    print("1. 🔄 更新软件:")
    print("   - 更新Chrome浏览器到最新版本")
    print("   - 更新ChromeDriver到匹配版本")
    print("   - 确保Selenium版本兼容")
    
    print("\n2. 🛡️ 权限设置:")
    print("   - 以管理员权限运行程序")
    print("   - 检查防火墙和杀毒软件设置")
    print("   - 确保临时目录有写入权限")
    
    print("\n3. 🔧 系统配置:")
    print("   - 关闭其他Chrome实例")
    print("   - 重启计算机清理进程")
    print("   - 检查系统内存是否充足")
    
    print("\n4. 🎯 程序配置:")
    print("   - 在多浏览器发送界面关闭'智能并发模式'")
    print("   - 使用单浏览器模式进行发送")
    print("   - 调整发送间隔和每账号发送数量")
    
    print("\n5. 🚀 临时解决方案:")
    print("   - 使用单浏览器发送模式")
    print("   - 减少同时账号数到1")
    print("   - 增加发送间隔时间")

def main():
    """主测试函数"""
    print("🚀 简化浏览器启动测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("系统信息检查", test_system_info),
        ("基础浏览器启动", test_basic_browser),
        ("回退配置测试", test_fallback_browser)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("🎯 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 浏览器基础功能正常！")
        print("   多浏览器并发发送应该可以工作")
    elif passed > 0:
        print("⚠️ 部分功能正常，建议使用单浏览器模式")
    else:
        print("❌ 浏览器启动存在问题")
    
    # 显示解决建议
    suggest_solutions()
    
    return passed >= 1  # 至少有一个测试通过就算成功

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
